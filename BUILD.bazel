load("@buildifier_prebuilt//:rules.bzl", "buildifier")
load("@gazelle//:def.bzl", "gazelle")
load("@io_bazel_rules_go//proto:compiler.bzl", "go_proto_compiler")

exports_files(
    ["buf.yaml"],
    visibility = ["//visibility:public"],
)
# gazelle:prefix github.com/MoeGolibrary/moego
# gazelle:go_grpc_compilers	@io_bazel_rules_go//proto:go_proto,@io_bazel_rules_go//proto:go_grpc_v2, //:pgv_plugin_go
# gazelle:exclude **/*.pb.validate.go
# gazelle:exclude _java_
# gazelle:exclude build

##
### # gazelle:resolve go github.com/envoyproxy/protoc-gen-validate @com_envoyproxy_protoc_gen_validate//:protoc-gen-validate
gazelle(
    name = "gazelle",
    # gazelle = ":gazelle-buf",
    prefix = "github.com/MoeGolibrary/moego",
)

gazelle(
    name = "gazelle-update-repos",
    args = [
        "-from_file=go.mod",
        "-to_macro=deps.bzl%go_dependencies",
        "-build_file_proto_mode=disable",
        "-prune",
    ],
    command = "update-repos",
    # gazelle = ":gazelle-buf",
)

go_proto_compiler(
    name = "pgv_plugin_go",
    options = ["lang=go"],
    plugin = "@com_envoyproxy_protoc_gen_validate//:protoc-gen-validate",
    suffix = ".pb.validate.go",
    valid_archive = False,
    visibility = ["//visibility:public"],
)

buildifier(
    name = "buildifier.check",
    exclude_patterns = [
        "./.git/*",
    ],
    lint_mode = "warn",
    mode = "diff",
)

buildifier(
    name = "buildifier.fix",
    exclude_patterns = [
        "./.git/*",
    ],
    lint_mode = "fix",
    mode = "fix",
)
