// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.7
// 	protoc        (unknown)
// source: backend/proto/fulfillment/v1/fulfillment_report_service.proto

package fulfillmentpb

import (
	v1 "github.com/MoeGolibrary/moego/backend/proto/pet/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 同步操作类型
type SyncOperation int32

const (
	// 未指定
	SyncOperation_SYNC_OPERATION_UNSPECIFIED SyncOperation = 0
	// 创建
	SyncOperation_CREATE SyncOperation = 1
	// 更新
	SyncOperation_UPDATE SyncOperation = 2
	// 创建或更新
	SyncOperation_UPSERT SyncOperation = 3
	// 删除
	SyncOperation_DELETE SyncOperation = 4
)

// Enum value maps for SyncOperation.
var (
	SyncOperation_name = map[int32]string{
		0: "SYNC_OPERATION_UNSPECIFIED",
		1: "CREATE",
		2: "UPDATE",
		3: "UPSERT",
		4: "DELETE",
	}
	SyncOperation_value = map[string]int32{
		"SYNC_OPERATION_UNSPECIFIED": 0,
		"CREATE":                     1,
		"UPDATE":                     2,
		"UPSERT":                     3,
		"DELETE":                     4,
	}
)

func (x SyncOperation) Enum() *SyncOperation {
	p := new(SyncOperation)
	*p = x
	return p
}

func (x SyncOperation) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SyncOperation) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_enumTypes[0].Descriptor()
}

func (SyncOperation) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_enumTypes[0]
}

func (x SyncOperation) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SyncOperation.Descriptor instead.
func (SyncOperation) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{0}
}

// 同步状态
// (-- api-linter: core::0216::synonyms=disabled --)
type SyncStatus int32

const (
	// 未指定
	SyncStatus_SYNC_STATUS_UNSPECIFIED SyncStatus = 0
	// 成功
	SyncStatus_SUCCESS SyncStatus = 1
	// 失败
	SyncStatus_FAILED SyncStatus = 2
	// 部分成功
	SyncStatus_PARTIAL_SUCCESS SyncStatus = 3
)

// Enum value maps for SyncStatus.
var (
	SyncStatus_name = map[int32]string{
		0: "SYNC_STATUS_UNSPECIFIED",
		1: "SUCCESS",
		2: "FAILED",
		3: "PARTIAL_SUCCESS",
	}
	SyncStatus_value = map[string]int32{
		"SYNC_STATUS_UNSPECIFIED": 0,
		"SUCCESS":                 1,
		"FAILED":                  2,
		"PARTIAL_SUCCESS":         3,
	}
)

func (x SyncStatus) Enum() *SyncStatus {
	p := new(SyncStatus)
	*p = x
	return p
}

func (x SyncStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SyncStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_enumTypes[1].Descriptor()
}

func (SyncStatus) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_enumTypes[1]
}

func (x SyncStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SyncStatus.Descriptor instead.
func (SyncStatus) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{1}
}

// GetFulfillmentReportTemplateRequest
type GetFulfillmentReportTemplateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// care type
	CareType      CareType `protobuf:"varint,3,opt,name=care_type,json=careType,proto3,enum=backend.proto.fulfillment.v1.CareType" json:"care_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFulfillmentReportTemplateRequest) Reset() {
	*x = GetFulfillmentReportTemplateRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFulfillmentReportTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFulfillmentReportTemplateRequest) ProtoMessage() {}

func (x *GetFulfillmentReportTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFulfillmentReportTemplateRequest.ProtoReflect.Descriptor instead.
func (*GetFulfillmentReportTemplateRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetFulfillmentReportTemplateRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetFulfillmentReportTemplateRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *GetFulfillmentReportTemplateRequest) GetCareType() CareType {
	if x != nil {
		return x.CareType
	}
	return CareType_CARE_TYPE_UNSPECIFIED
}

// GetFulfillmentReportTemplateResponse
type GetFulfillmentReportTemplateResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// template
	Template      *FulfillmentReportTemplate `protobuf:"bytes,1,opt,name=template,proto3" json:"template,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFulfillmentReportTemplateResponse) Reset() {
	*x = GetFulfillmentReportTemplateResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFulfillmentReportTemplateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFulfillmentReportTemplateResponse) ProtoMessage() {}

func (x *GetFulfillmentReportTemplateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFulfillmentReportTemplateResponse.ProtoReflect.Descriptor instead.
func (*GetFulfillmentReportTemplateResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetFulfillmentReportTemplateResponse) GetTemplate() *FulfillmentReportTemplate {
	if x != nil {
		return x.Template
	}
	return nil
}

// UpdateFulfillmentReportTemplateRequest
type UpdateFulfillmentReportTemplateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// id
	Id int64 `protobuf:"varint,4,opt,name=id,proto3" json:"id,omitempty"`
	// care type
	CareType CareType `protobuf:"varint,5,opt,name=care_type,json=careType,proto3,enum=backend.proto.fulfillment.v1.CareType" json:"care_type,omitempty"`
	// title
	Title string `protobuf:"bytes,6,opt,name=title,proto3" json:"title,omitempty"`
	// theme color
	ThemeColor string `protobuf:"bytes,7,opt,name=theme_color,json=themeColor,proto3" json:"theme_color,omitempty"`
	// light theme color
	LightThemeColor string `protobuf:"bytes,8,opt,name=light_theme_color,json=lightThemeColor,proto3" json:"light_theme_color,omitempty"`
	// theme code
	ThemeCode string `protobuf:"bytes,9,opt,name=theme_code,json=themeCode,proto3" json:"theme_code,omitempty"`
	// thank you message
	ThankYouMessage string `protobuf:"bytes,10,opt,name=thank_you_message,json=thankYouMessage,proto3" json:"thank_you_message,omitempty"`
	// show showcase
	ShowShowcase bool `protobuf:"varint,11,opt,name=show_showcase,json=showShowcase,proto3" json:"show_showcase,omitempty"`
	// show overall feedback
	ShowOverallFeedback bool `protobuf:"varint,12,opt,name=show_overall_feedback,json=showOverallFeedback,proto3" json:"show_overall_feedback,omitempty"`
	// show pet condition
	ShowPetCondition bool `protobuf:"varint,13,opt,name=show_pet_condition,json=showPetCondition,proto3" json:"show_pet_condition,omitempty"`
	// show staff
	ShowStaff bool `protobuf:"varint,14,opt,name=show_staff,json=showStaff,proto3" json:"show_staff,omitempty"`
	// show customize feedback
	ShowCustomizeFeedback bool `protobuf:"varint,15,opt,name=show_customize_feedback,json=showCustomizeFeedback,proto3" json:"show_customize_feedback,omitempty"`
	// show next appointment
	ShowNextAppointment bool `protobuf:"varint,16,opt,name=show_next_appointment,json=showNextAppointment,proto3" json:"show_next_appointment,omitempty"`
	// next appointment date format type
	NextAppointmentDateFormatType NextAppointmentDateFormatType `protobuf:"varint,17,opt,name=next_appointment_date_format_type,json=nextAppointmentDateFormatType,proto3,enum=backend.proto.fulfillment.v1.NextAppointmentDateFormatType" json:"next_appointment_date_format_type,omitempty"`
	// show review booster
	ShowReviewBooster bool `protobuf:"varint,18,opt,name=show_review_booster,json=showReviewBooster,proto3" json:"show_review_booster,omitempty"`
	// show yelp review icon
	ShowYelpReview bool `protobuf:"varint,19,opt,name=show_yelp_review,json=showYelpReview,proto3" json:"show_yelp_review,omitempty"`
	// yelp review icon jump link
	YelpReviewLink string `protobuf:"bytes,20,opt,name=yelp_review_link,json=yelpReviewLink,proto3" json:"yelp_review_link,omitempty"`
	// show google review icon
	ShowGoogleReview bool `protobuf:"varint,21,opt,name=show_google_review,json=showGoogleReview,proto3" json:"show_google_review,omitempty"`
	// google review icon jump link
	GoogleReviewLink string `protobuf:"bytes,22,opt,name=google_review_link,json=googleReviewLink,proto3" json:"google_review_link,omitempty"`
	// show facebook review icon
	ShowFacebookReview bool `protobuf:"varint,23,opt,name=show_facebook_review,json=showFacebookReview,proto3" json:"show_facebook_review,omitempty"`
	// facebook review icon jump link
	FacebookReviewLink string `protobuf:"bytes,24,opt,name=facebook_review_link,json=facebookReviewLink,proto3" json:"facebook_review_link,omitempty"`
	// questions
	Questions []*UpdateFulfillmentReportTemplateRequest_UpdateQuestion `protobuf:"bytes,25,rep,name=questions,proto3" json:"questions,omitempty"`
	// delete questions
	DeleteQuestionIds []int64 `protobuf:"varint,26,rep,packed,name=delete_question_ids,json=deleteQuestionIds,proto3" json:"delete_question_ids,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *UpdateFulfillmentReportTemplateRequest) Reset() {
	*x = UpdateFulfillmentReportTemplateRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateFulfillmentReportTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateFulfillmentReportTemplateRequest) ProtoMessage() {}

func (x *UpdateFulfillmentReportTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateFulfillmentReportTemplateRequest.ProtoReflect.Descriptor instead.
func (*UpdateFulfillmentReportTemplateRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateFulfillmentReportTemplateRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpdateFulfillmentReportTemplateRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *UpdateFulfillmentReportTemplateRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *UpdateFulfillmentReportTemplateRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateFulfillmentReportTemplateRequest) GetCareType() CareType {
	if x != nil {
		return x.CareType
	}
	return CareType_CARE_TYPE_UNSPECIFIED
}

func (x *UpdateFulfillmentReportTemplateRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UpdateFulfillmentReportTemplateRequest) GetThemeColor() string {
	if x != nil {
		return x.ThemeColor
	}
	return ""
}

func (x *UpdateFulfillmentReportTemplateRequest) GetLightThemeColor() string {
	if x != nil {
		return x.LightThemeColor
	}
	return ""
}

func (x *UpdateFulfillmentReportTemplateRequest) GetThemeCode() string {
	if x != nil {
		return x.ThemeCode
	}
	return ""
}

func (x *UpdateFulfillmentReportTemplateRequest) GetThankYouMessage() string {
	if x != nil {
		return x.ThankYouMessage
	}
	return ""
}

func (x *UpdateFulfillmentReportTemplateRequest) GetShowShowcase() bool {
	if x != nil {
		return x.ShowShowcase
	}
	return false
}

func (x *UpdateFulfillmentReportTemplateRequest) GetShowOverallFeedback() bool {
	if x != nil {
		return x.ShowOverallFeedback
	}
	return false
}

func (x *UpdateFulfillmentReportTemplateRequest) GetShowPetCondition() bool {
	if x != nil {
		return x.ShowPetCondition
	}
	return false
}

func (x *UpdateFulfillmentReportTemplateRequest) GetShowStaff() bool {
	if x != nil {
		return x.ShowStaff
	}
	return false
}

func (x *UpdateFulfillmentReportTemplateRequest) GetShowCustomizeFeedback() bool {
	if x != nil {
		return x.ShowCustomizeFeedback
	}
	return false
}

func (x *UpdateFulfillmentReportTemplateRequest) GetShowNextAppointment() bool {
	if x != nil {
		return x.ShowNextAppointment
	}
	return false
}

func (x *UpdateFulfillmentReportTemplateRequest) GetNextAppointmentDateFormatType() NextAppointmentDateFormatType {
	if x != nil {
		return x.NextAppointmentDateFormatType
	}
	return NextAppointmentDateFormatType_NEXT_APPOINTMENT_DATE_FORMAT_TYPE_UNSPECIFIED
}

func (x *UpdateFulfillmentReportTemplateRequest) GetShowReviewBooster() bool {
	if x != nil {
		return x.ShowReviewBooster
	}
	return false
}

func (x *UpdateFulfillmentReportTemplateRequest) GetShowYelpReview() bool {
	if x != nil {
		return x.ShowYelpReview
	}
	return false
}

func (x *UpdateFulfillmentReportTemplateRequest) GetYelpReviewLink() string {
	if x != nil {
		return x.YelpReviewLink
	}
	return ""
}

func (x *UpdateFulfillmentReportTemplateRequest) GetShowGoogleReview() bool {
	if x != nil {
		return x.ShowGoogleReview
	}
	return false
}

func (x *UpdateFulfillmentReportTemplateRequest) GetGoogleReviewLink() string {
	if x != nil {
		return x.GoogleReviewLink
	}
	return ""
}

func (x *UpdateFulfillmentReportTemplateRequest) GetShowFacebookReview() bool {
	if x != nil {
		return x.ShowFacebookReview
	}
	return false
}

func (x *UpdateFulfillmentReportTemplateRequest) GetFacebookReviewLink() string {
	if x != nil {
		return x.FacebookReviewLink
	}
	return ""
}

func (x *UpdateFulfillmentReportTemplateRequest) GetQuestions() []*UpdateFulfillmentReportTemplateRequest_UpdateQuestion {
	if x != nil {
		return x.Questions
	}
	return nil
}

func (x *UpdateFulfillmentReportTemplateRequest) GetDeleteQuestionIds() []int64 {
	if x != nil {
		return x.DeleteQuestionIds
	}
	return nil
}

// UpdateFulfillmentReportTemplateResponse
type UpdateFulfillmentReportTemplateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateFulfillmentReportTemplateResponse) Reset() {
	*x = UpdateFulfillmentReportTemplateResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateFulfillmentReportTemplateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateFulfillmentReportTemplateResponse) ProtoMessage() {}

func (x *UpdateFulfillmentReportTemplateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateFulfillmentReportTemplateResponse.ProtoReflect.Descriptor instead.
func (*UpdateFulfillmentReportTemplateResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{3}
}

// get fulfillment report
type GetFulfillmentReportRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// report id (if not provided, must provide appointment_id, pet_id, care_type and service_date)
	ReportId *int64 `protobuf:"varint,1,opt,name=report_id,json=reportId,proto3,oneof" json:"report_id,omitempty"`
	// appointment id
	AppointmentId *int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3,oneof" json:"appointment_id,omitempty"`
	// pet id
	PetId *int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3,oneof" json:"pet_id,omitempty"`
	// care type
	CareType *CareType `protobuf:"varint,4,opt,name=care_type,json=careType,proto3,enum=backend.proto.fulfillment.v1.CareType,oneof" json:"care_type,omitempty"`
	// service date, if care type is BOARDING/DAYCARE, this field is required
	// (-- api-linter: core::0142::time-field-type=disabled --)
	ServiceDate   *string `protobuf:"bytes,5,opt,name=service_date,json=serviceDate,proto3,oneof" json:"service_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFulfillmentReportRequest) Reset() {
	*x = GetFulfillmentReportRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFulfillmentReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFulfillmentReportRequest) ProtoMessage() {}

func (x *GetFulfillmentReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFulfillmentReportRequest.ProtoReflect.Descriptor instead.
func (*GetFulfillmentReportRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetFulfillmentReportRequest) GetReportId() int64 {
	if x != nil && x.ReportId != nil {
		return *x.ReportId
	}
	return 0
}

func (x *GetFulfillmentReportRequest) GetAppointmentId() int64 {
	if x != nil && x.AppointmentId != nil {
		return *x.AppointmentId
	}
	return 0
}

func (x *GetFulfillmentReportRequest) GetPetId() int64 {
	if x != nil && x.PetId != nil {
		return *x.PetId
	}
	return 0
}

func (x *GetFulfillmentReportRequest) GetCareType() CareType {
	if x != nil && x.CareType != nil {
		return *x.CareType
	}
	return CareType_CARE_TYPE_UNSPECIFIED
}

func (x *GetFulfillmentReportRequest) GetServiceDate() string {
	if x != nil && x.ServiceDate != nil {
		return *x.ServiceDate
	}
	return ""
}

// GetFulfillmentReportResponse
type GetFulfillmentReportResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// fulfillment report
	FulfillmentReport *FulfillmentReport `protobuf:"bytes,1,opt,name=fulfillment_report,json=fulfillmentReport,proto3" json:"fulfillment_report,omitempty"`
	// is need refresh
	IsNeedRefresh bool `protobuf:"varint,2,opt,name=is_need_refresh,json=isNeedRefresh,proto3" json:"is_need_refresh,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFulfillmentReportResponse) Reset() {
	*x = GetFulfillmentReportResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFulfillmentReportResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFulfillmentReportResponse) ProtoMessage() {}

func (x *GetFulfillmentReportResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFulfillmentReportResponse.ProtoReflect.Descriptor instead.
func (*GetFulfillmentReportResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetFulfillmentReportResponse) GetFulfillmentReport() *FulfillmentReport {
	if x != nil {
		return x.FulfillmentReport
	}
	return nil
}

func (x *GetFulfillmentReportResponse) GetIsNeedRefresh() bool {
	if x != nil {
		return x.IsNeedRefresh
	}
	return false
}

// update fulfillment report
type UpdateFulfillmentReportRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// operator staff id
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// id
	Id *int64 `protobuf:"varint,4,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,5,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,6,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,7,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// care type
	CareType CareType `protobuf:"varint,8,opt,name=care_type,json=careType,proto3,enum=backend.proto.fulfillment.v1.CareType" json:"care_type,omitempty"`
	// service date, if care type is BOARDING/DAYCARE, this field is required
	// (-- api-linter: core::0142::time-field-type=disabled --)
	ServiceDate *string `protobuf:"bytes,10,opt,name=service_date,json=serviceDate,proto3,oneof" json:"service_date,omitempty"`
	// report content
	Content *FulfillmentReportContent `protobuf:"bytes,11,opt,name=content,proto3" json:"content,omitempty"`
	// theme code
	ThemeCode     *string `protobuf:"bytes,12,opt,name=theme_code,json=themeCode,proto3,oneof" json:"theme_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateFulfillmentReportRequest) Reset() {
	*x = UpdateFulfillmentReportRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateFulfillmentReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateFulfillmentReportRequest) ProtoMessage() {}

func (x *UpdateFulfillmentReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateFulfillmentReportRequest.ProtoReflect.Descriptor instead.
func (*UpdateFulfillmentReportRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateFulfillmentReportRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpdateFulfillmentReportRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *UpdateFulfillmentReportRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *UpdateFulfillmentReportRequest) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *UpdateFulfillmentReportRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *UpdateFulfillmentReportRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *UpdateFulfillmentReportRequest) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *UpdateFulfillmentReportRequest) GetCareType() CareType {
	if x != nil {
		return x.CareType
	}
	return CareType_CARE_TYPE_UNSPECIFIED
}

func (x *UpdateFulfillmentReportRequest) GetServiceDate() string {
	if x != nil && x.ServiceDate != nil {
		return *x.ServiceDate
	}
	return ""
}

func (x *UpdateFulfillmentReportRequest) GetContent() *FulfillmentReportContent {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *UpdateFulfillmentReportRequest) GetThemeCode() string {
	if x != nil && x.ThemeCode != nil {
		return *x.ThemeCode
	}
	return ""
}

// UpdateFulfillmentReportResponse
type UpdateFulfillmentReportResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// fulfillment report
	FulfillmentReport *FulfillmentReport `protobuf:"bytes,1,opt,name=fulfillment_report,json=fulfillmentReport,proto3" json:"fulfillment_report,omitempty"`
	// is need refresh
	IsNeedRefresh bool `protobuf:"varint,2,opt,name=is_need_refresh,json=isNeedRefresh,proto3" json:"is_need_refresh,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateFulfillmentReportResponse) Reset() {
	*x = UpdateFulfillmentReportResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateFulfillmentReportResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateFulfillmentReportResponse) ProtoMessage() {}

func (x *UpdateFulfillmentReportResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateFulfillmentReportResponse.ProtoReflect.Descriptor instead.
func (*UpdateFulfillmentReportResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateFulfillmentReportResponse) GetFulfillmentReport() *FulfillmentReport {
	if x != nil {
		return x.FulfillmentReport
	}
	return nil
}

func (x *UpdateFulfillmentReportResponse) GetIsNeedRefresh() bool {
	if x != nil {
		return x.IsNeedRefresh
	}
	return false
}

// get fulfillment report summary info
type GetFulfillmentReportSummaryInfoRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// fulfillment report id (if not provided, must provide uuid)
	FulfillmentReportId *int64 `protobuf:"varint,1,opt,name=fulfillment_report_id,json=fulfillmentReportId,proto3,oneof" json:"fulfillment_report_id,omitempty"`
	// uuid (if not provided, must provide fulfillment report id)
	Uuid          *string `protobuf:"bytes,2,opt,name=uuid,proto3,oneof" json:"uuid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFulfillmentReportSummaryInfoRequest) Reset() {
	*x = GetFulfillmentReportSummaryInfoRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFulfillmentReportSummaryInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFulfillmentReportSummaryInfoRequest) ProtoMessage() {}

func (x *GetFulfillmentReportSummaryInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFulfillmentReportSummaryInfoRequest.ProtoReflect.Descriptor instead.
func (*GetFulfillmentReportSummaryInfoRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetFulfillmentReportSummaryInfoRequest) GetFulfillmentReportId() int64 {
	if x != nil && x.FulfillmentReportId != nil {
		return *x.FulfillmentReportId
	}
	return 0
}

func (x *GetFulfillmentReportSummaryInfoRequest) GetUuid() string {
	if x != nil && x.Uuid != nil {
		return *x.Uuid
	}
	return ""
}

// GetFulfillmentReportSummaryInfoResponse
type GetFulfillmentReportSummaryInfoResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// summary info
	SummaryInfo   *FulfillmentReportCardSummaryInfo `protobuf:"bytes,1,opt,name=summary_info,json=summaryInfo,proto3" json:"summary_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFulfillmentReportSummaryInfoResponse) Reset() {
	*x = GetFulfillmentReportSummaryInfoResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFulfillmentReportSummaryInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFulfillmentReportSummaryInfoResponse) ProtoMessage() {}

func (x *GetFulfillmentReportSummaryInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFulfillmentReportSummaryInfoResponse.ProtoReflect.Descriptor instead.
func (*GetFulfillmentReportSummaryInfoResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetFulfillmentReportSummaryInfoResponse) GetSummaryInfo() *FulfillmentReportCardSummaryInfo {
	if x != nil {
		return x.SummaryInfo
	}
	return nil
}

// GetFulfillmentReportRecordsRequest
type GetFulfillmentReportRecordsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,3,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// service date
	// (-- api-linter: core::0142::time-field-type=disabled --)
	ServiceDate   *string `protobuf:"bytes,4,opt,name=service_date,json=serviceDate,proto3,oneof" json:"service_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFulfillmentReportRecordsRequest) Reset() {
	*x = GetFulfillmentReportRecordsRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFulfillmentReportRecordsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFulfillmentReportRecordsRequest) ProtoMessage() {}

func (x *GetFulfillmentReportRecordsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFulfillmentReportRecordsRequest.ProtoReflect.Descriptor instead.
func (*GetFulfillmentReportRecordsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetFulfillmentReportRecordsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetFulfillmentReportRecordsRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *GetFulfillmentReportRecordsRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *GetFulfillmentReportRecordsRequest) GetServiceDate() string {
	if x != nil && x.ServiceDate != nil {
		return *x.ServiceDate
	}
	return ""
}

// GetFulfillmentReportRecordsResponse
type GetFulfillmentReportRecordsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// fulfillment report records
	FulfillmentReportRecords []*GetFulfillmentReportRecordsResponse_FulfillmentReportRecords `protobuf:"bytes,1,rep,name=fulfillment_report_records,json=fulfillmentReportRecords,proto3" json:"fulfillment_report_records,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *GetFulfillmentReportRecordsResponse) Reset() {
	*x = GetFulfillmentReportRecordsResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFulfillmentReportRecordsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFulfillmentReportRecordsResponse) ProtoMessage() {}

func (x *GetFulfillmentReportRecordsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFulfillmentReportRecordsResponse.ProtoReflect.Descriptor instead.
func (*GetFulfillmentReportRecordsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetFulfillmentReportRecordsResponse) GetFulfillmentReportRecords() []*GetFulfillmentReportRecordsResponse_FulfillmentReportRecords {
	if x != nil {
		return x.FulfillmentReportRecords
	}
	return nil
}

// GetFulfillmentReportPreviewRequest
type GetFulfillmentReportPreviewRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// care type
	CareType CareType `protobuf:"varint,3,opt,name=care_type,json=careType,proto3,enum=backend.proto.fulfillment.v1.CareType" json:"care_type,omitempty"`
	// report id
	ReportId *int64 `protobuf:"varint,4,opt,name=report_id,json=reportId,proto3,oneof" json:"report_id,omitempty"`
	// theme code
	ThemeCode     *string `protobuf:"bytes,5,opt,name=theme_code,json=themeCode,proto3,oneof" json:"theme_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFulfillmentReportPreviewRequest) Reset() {
	*x = GetFulfillmentReportPreviewRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFulfillmentReportPreviewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFulfillmentReportPreviewRequest) ProtoMessage() {}

func (x *GetFulfillmentReportPreviewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFulfillmentReportPreviewRequest.ProtoReflect.Descriptor instead.
func (*GetFulfillmentReportPreviewRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetFulfillmentReportPreviewRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetFulfillmentReportPreviewRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *GetFulfillmentReportPreviewRequest) GetCareType() CareType {
	if x != nil {
		return x.CareType
	}
	return CareType_CARE_TYPE_UNSPECIFIED
}

func (x *GetFulfillmentReportPreviewRequest) GetReportId() int64 {
	if x != nil && x.ReportId != nil {
		return *x.ReportId
	}
	return 0
}

func (x *GetFulfillmentReportPreviewRequest) GetThemeCode() string {
	if x != nil && x.ThemeCode != nil {
		return *x.ThemeCode
	}
	return ""
}

// GetFulfillmentReportPreviewResponse
type GetFulfillmentReportPreviewResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// summary info
	SummaryInfo *FulfillmentReportCardSummaryInfo `protobuf:"bytes,1,opt,name=summary_info,json=summaryInfo,proto3" json:"summary_info,omitempty"`
	// fulfillment report sample value
	SampleValue   *FulfillmentReportSampleValue `protobuf:"bytes,2,opt,name=sample_value,json=sampleValue,proto3" json:"sample_value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFulfillmentReportPreviewResponse) Reset() {
	*x = GetFulfillmentReportPreviewResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFulfillmentReportPreviewResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFulfillmentReportPreviewResponse) ProtoMessage() {}

func (x *GetFulfillmentReportPreviewResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFulfillmentReportPreviewResponse.ProtoReflect.Descriptor instead.
func (*GetFulfillmentReportPreviewResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetFulfillmentReportPreviewResponse) GetSummaryInfo() *FulfillmentReportCardSummaryInfo {
	if x != nil {
		return x.SummaryInfo
	}
	return nil
}

func (x *GetFulfillmentReportPreviewResponse) GetSampleValue() *FulfillmentReportSampleValue {
	if x != nil {
		return x.SampleValue
	}
	return nil
}

// ListFulfillmentThemeConfigRequest
// (-- api-linter: core::0158::request-page-token-field=disabled --)
// (-- api-linter: core::0132::request-parent-required=disabled --)
// (-- api-linter: core::0158::request-page-size-field=disabled --)
type ListFulfillmentThemeConfigRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company id
	CompanyId     int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListFulfillmentThemeConfigRequest) Reset() {
	*x = ListFulfillmentThemeConfigRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListFulfillmentThemeConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFulfillmentThemeConfigRequest) ProtoMessage() {}

func (x *ListFulfillmentThemeConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFulfillmentThemeConfigRequest.ProtoReflect.Descriptor instead.
func (*ListFulfillmentThemeConfigRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{14}
}

func (x *ListFulfillmentThemeConfigRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// ListFulfillmentThemeConfigResponse
// (-- api-linter: core::0158::response-next-page-token-field=disabled --)
// (-- api-linter: core::0132::response-unknown-fields=disabled --)
type ListFulfillmentThemeConfigResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// theme configs
	ThemeConfigs  []*FulfillmentReportThemeConfig `protobuf:"bytes,1,rep,name=theme_configs,json=themeConfigs,proto3" json:"theme_configs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListFulfillmentThemeConfigResponse) Reset() {
	*x = ListFulfillmentThemeConfigResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListFulfillmentThemeConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFulfillmentThemeConfigResponse) ProtoMessage() {}

func (x *ListFulfillmentThemeConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFulfillmentThemeConfigResponse.ProtoReflect.Descriptor instead.
func (*ListFulfillmentThemeConfigResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{15}
}

func (x *ListFulfillmentThemeConfigResponse) GetThemeConfigs() []*FulfillmentReportThemeConfig {
	if x != nil {
		return x.ThemeConfigs
	}
	return nil
}

// GenerateMessageContentRequest
type GenerateMessageContentRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// service date, if care type is BOARDING/DAYCARE, this field is required
	// (-- api-linter: core::0142::time-field-type=disabled --)
	ServiceDate *string `protobuf:"bytes,4,opt,name=service_date,json=serviceDate,proto3,oneof" json:"service_date,omitempty"`
	// care type
	CareType      CareType `protobuf:"varint,5,opt,name=care_type,json=careType,proto3,enum=backend.proto.fulfillment.v1.CareType" json:"care_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenerateMessageContentRequest) Reset() {
	*x = GenerateMessageContentRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateMessageContentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateMessageContentRequest) ProtoMessage() {}

func (x *GenerateMessageContentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateMessageContentRequest.ProtoReflect.Descriptor instead.
func (*GenerateMessageContentRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{16}
}

func (x *GenerateMessageContentRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GenerateMessageContentRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *GenerateMessageContentRequest) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *GenerateMessageContentRequest) GetServiceDate() string {
	if x != nil && x.ServiceDate != nil {
		return *x.ServiceDate
	}
	return ""
}

func (x *GenerateMessageContentRequest) GetCareType() CareType {
	if x != nil {
		return x.CareType
	}
	return CareType_CARE_TYPE_UNSPECIFIED
}

// GenerateMessageContentResponse
type GenerateMessageContentResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// message content
	MessageContent string `protobuf:"bytes,1,opt,name=message_content,json=messageContent,proto3" json:"message_content,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GenerateMessageContentResponse) Reset() {
	*x = GenerateMessageContentResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateMessageContentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateMessageContentResponse) ProtoMessage() {}

func (x *GenerateMessageContentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateMessageContentResponse.ProtoReflect.Descriptor instead.
func (*GenerateMessageContentResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{17}
}

func (x *GenerateMessageContentResponse) GetMessageContent() string {
	if x != nil {
		return x.MessageContent
	}
	return ""
}

// SendFulfillmentReportRequest
type SendFulfillmentReportRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// fulfillment report id
	FulfillmentReportId int64 `protobuf:"varint,1,opt,name=fulfillment_report_id,json=fulfillmentReportId,proto3" json:"fulfillment_report_id,omitempty"`
	// send method
	SendMethod SendMethod `protobuf:"varint,2,opt,name=send_method,json=sendMethod,proto3,enum=backend.proto.fulfillment.v1.SendMethod" json:"send_method,omitempty"`
	// recipient emails, if send method is EMAIL, this field is required
	RecipientEmails []string `protobuf:"bytes,3,rep,name=recipient_emails,json=recipientEmails,proto3" json:"recipient_emails,omitempty"`
	// email subject, if send method is EMAIL, this field is required
	EmailSubject  *string `protobuf:"bytes,4,opt,name=email_subject,json=emailSubject,proto3,oneof" json:"email_subject,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendFulfillmentReportRequest) Reset() {
	*x = SendFulfillmentReportRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendFulfillmentReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendFulfillmentReportRequest) ProtoMessage() {}

func (x *SendFulfillmentReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendFulfillmentReportRequest.ProtoReflect.Descriptor instead.
func (*SendFulfillmentReportRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{18}
}

func (x *SendFulfillmentReportRequest) GetFulfillmentReportId() int64 {
	if x != nil {
		return x.FulfillmentReportId
	}
	return 0
}

func (x *SendFulfillmentReportRequest) GetSendMethod() SendMethod {
	if x != nil {
		return x.SendMethod
	}
	return SendMethod_SEND_METHOD_UNSPECIFIED
}

func (x *SendFulfillmentReportRequest) GetRecipientEmails() []string {
	if x != nil {
		return x.RecipientEmails
	}
	return nil
}

func (x *SendFulfillmentReportRequest) GetEmailSubject() string {
	if x != nil && x.EmailSubject != nil {
		return *x.EmailSubject
	}
	return ""
}

// SendFulfillmentReportResponse
type SendFulfillmentReportResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// send result
	SendResult    *FulfillmentReportSendResult `protobuf:"bytes,1,opt,name=send_result,json=sendResult,proto3" json:"send_result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendFulfillmentReportResponse) Reset() {
	*x = SendFulfillmentReportResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendFulfillmentReportResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendFulfillmentReportResponse) ProtoMessage() {}

func (x *SendFulfillmentReportResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendFulfillmentReportResponse.ProtoReflect.Descriptor instead.
func (*SendFulfillmentReportResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{19}
}

func (x *SendFulfillmentReportResponse) GetSendResult() *FulfillmentReportSendResult {
	if x != nil {
		return x.SendResult
	}
	return nil
}

// GetFulfillmentReportSendHistoryRequest
type GetFulfillmentReportSendHistoryRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// care type
	CareType      CareType `protobuf:"varint,4,opt,name=care_type,json=careType,proto3,enum=backend.proto.fulfillment.v1.CareType" json:"care_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFulfillmentReportSendHistoryRequest) Reset() {
	*x = GetFulfillmentReportSendHistoryRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFulfillmentReportSendHistoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFulfillmentReportSendHistoryRequest) ProtoMessage() {}

func (x *GetFulfillmentReportSendHistoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFulfillmentReportSendHistoryRequest.ProtoReflect.Descriptor instead.
func (*GetFulfillmentReportSendHistoryRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{20}
}

func (x *GetFulfillmentReportSendHistoryRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetFulfillmentReportSendHistoryRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *GetFulfillmentReportSendHistoryRequest) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *GetFulfillmentReportSendHistoryRequest) GetCareType() CareType {
	if x != nil {
		return x.CareType
	}
	return CareType_CARE_TYPE_UNSPECIFIED
}

// GetFulfillmentReportSendHistoryResponse
type GetFulfillmentReportSendHistoryResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// send history
	SendRecords   []*FulfillmentReportSendRecord `protobuf:"bytes,1,rep,name=send_records,json=sendRecords,proto3" json:"send_records,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFulfillmentReportSendHistoryResponse) Reset() {
	*x = GetFulfillmentReportSendHistoryResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFulfillmentReportSendHistoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFulfillmentReportSendHistoryResponse) ProtoMessage() {}

func (x *GetFulfillmentReportSendHistoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFulfillmentReportSendHistoryResponse.ProtoReflect.Descriptor instead.
func (*GetFulfillmentReportSendHistoryResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{21}
}

func (x *GetFulfillmentReportSendHistoryResponse) GetSendRecords() []*FulfillmentReportSendRecord {
	if x != nil {
		return x.SendRecords
	}
	return nil
}

// GetFulfillmentReportSendResultRequest
type GetFulfillmentReportSendResultRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// fulfillment report id, if not provided, must provide appointment_id, pet_id, care_type and service_date
	FulfillmentReportId *int64 `protobuf:"varint,1,opt,name=fulfillment_report_id,json=fulfillmentReportId,proto3,oneof" json:"fulfillment_report_id,omitempty"`
	// appointment id
	AppointmentId *int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3,oneof" json:"appointment_id,omitempty"`
	// pet id
	PetId *int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3,oneof" json:"pet_id,omitempty"`
	// care type
	CareType *CareType `protobuf:"varint,4,opt,name=care_type,json=careType,proto3,enum=backend.proto.fulfillment.v1.CareType,oneof" json:"care_type,omitempty"`
	// service date
	// (-- api-linter: core::0142::time-field-type=disabled --)
	ServiceDate   *string `protobuf:"bytes,5,opt,name=service_date,json=serviceDate,proto3,oneof" json:"service_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFulfillmentReportSendResultRequest) Reset() {
	*x = GetFulfillmentReportSendResultRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFulfillmentReportSendResultRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFulfillmentReportSendResultRequest) ProtoMessage() {}

func (x *GetFulfillmentReportSendResultRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFulfillmentReportSendResultRequest.ProtoReflect.Descriptor instead.
func (*GetFulfillmentReportSendResultRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{22}
}

func (x *GetFulfillmentReportSendResultRequest) GetFulfillmentReportId() int64 {
	if x != nil && x.FulfillmentReportId != nil {
		return *x.FulfillmentReportId
	}
	return 0
}

func (x *GetFulfillmentReportSendResultRequest) GetAppointmentId() int64 {
	if x != nil && x.AppointmentId != nil {
		return *x.AppointmentId
	}
	return 0
}

func (x *GetFulfillmentReportSendResultRequest) GetPetId() int64 {
	if x != nil && x.PetId != nil {
		return *x.PetId
	}
	return 0
}

func (x *GetFulfillmentReportSendResultRequest) GetCareType() CareType {
	if x != nil && x.CareType != nil {
		return *x.CareType
	}
	return CareType_CARE_TYPE_UNSPECIFIED
}

func (x *GetFulfillmentReportSendResultRequest) GetServiceDate() string {
	if x != nil && x.ServiceDate != nil {
		return *x.ServiceDate
	}
	return ""
}

// GetFulfillmentReportSendResultResponse
type GetFulfillmentReportSendResultResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// send result
	SendResults   []*FulfillmentReportSendResult `protobuf:"bytes,1,rep,name=send_results,json=sendResults,proto3" json:"send_results,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFulfillmentReportSendResultResponse) Reset() {
	*x = GetFulfillmentReportSendResultResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFulfillmentReportSendResultResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFulfillmentReportSendResultResponse) ProtoMessage() {}

func (x *GetFulfillmentReportSendResultResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFulfillmentReportSendResultResponse.ProtoReflect.Descriptor instead.
func (*GetFulfillmentReportSendResultResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{23}
}

func (x *GetFulfillmentReportSendResultResponse) GetSendResults() []*FulfillmentReportSendResult {
	if x != nil {
		return x.SendResults
	}
	return nil
}

// ListFulfillmentReportRequest
// (-- api-linter: core::0158::request-page-token-field=disabled --)
// (-- api-linter: core::0132::request-parent-required=disabled --)
// (-- api-linter: core::0158::request-page-size-field=disabled --)
type ListFulfillmentReportRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// filter
	Filter *ListFulfillmentReportConfigFilter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
	// pagination
	Pagination    *PaginationRef `protobuf:"bytes,4,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListFulfillmentReportRequest) Reset() {
	*x = ListFulfillmentReportRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListFulfillmentReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFulfillmentReportRequest) ProtoMessage() {}

func (x *ListFulfillmentReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFulfillmentReportRequest.ProtoReflect.Descriptor instead.
func (*ListFulfillmentReportRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{24}
}

func (x *ListFulfillmentReportRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListFulfillmentReportRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListFulfillmentReportRequest) GetFilter() *ListFulfillmentReportConfigFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListFulfillmentReportRequest) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// ListFulfillmentReportResponse
// (-- api-linter: core::0158::response-next-page-token-field=disabled --)
type ListFulfillmentReportResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// fulfillment report cards
	// (-- api-linter: core::0132::response-unknown-fields=disabled --)
	FulfillmentReportCards []*ListFulfillmentReportResponse_FulfillmentReportCard `protobuf:"bytes,1,rep,name=fulfillment_report_cards,json=fulfillmentReportCards,proto3" json:"fulfillment_report_cards,omitempty"`
	// 分页信息
	// (-- api-linter: core::0132::response-unknown-fields=disabled
	//
	//	aip.dev/not-precedent: 必要的参数 --)
	Pagination *PaginationRef `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// 总条数
	// (-- api-linter: core::0132::response-unknown-fields=disabled
	//
	//	aip.dev/not-precedent: 必要的参数 --)
	Total         int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListFulfillmentReportResponse) Reset() {
	*x = ListFulfillmentReportResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListFulfillmentReportResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFulfillmentReportResponse) ProtoMessage() {}

func (x *ListFulfillmentReportResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFulfillmentReportResponse.ProtoReflect.Descriptor instead.
func (*ListFulfillmentReportResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{25}
}

func (x *ListFulfillmentReportResponse) GetFulfillmentReportCards() []*ListFulfillmentReportResponse_FulfillmentReportCard {
	if x != nil {
		return x.FulfillmentReportCards
	}
	return nil
}

func (x *ListFulfillmentReportResponse) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListFulfillmentReportResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// list fulfillment report config filter
type ListFulfillmentReportConfigFilter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// report card status
	Status *ReportStatus `protobuf:"varint,1,opt,name=status,proto3,enum=backend.proto.fulfillment.v1.ReportStatus,oneof" json:"status,omitempty"`
	// care types
	CareTypes []CareType `protobuf:"varint,2,rep,packed,name=care_types,json=careTypes,proto3,enum=backend.proto.fulfillment.v1.CareType" json:"care_types,omitempty"`
	// start date
	// (-- api-linter: core::0142::time-field-type=disabled --)
	StartDate *string `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// end date
	// (-- api-linter: core::0142::time-field-type=disabled --)
	EndDate *string `protobuf:"bytes,4,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
	// pet id
	PetId         *int64 `protobuf:"varint,5,opt,name=pet_id,json=petId,proto3,oneof" json:"pet_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListFulfillmentReportConfigFilter) Reset() {
	*x = ListFulfillmentReportConfigFilter{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListFulfillmentReportConfigFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFulfillmentReportConfigFilter) ProtoMessage() {}

func (x *ListFulfillmentReportConfigFilter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFulfillmentReportConfigFilter.ProtoReflect.Descriptor instead.
func (*ListFulfillmentReportConfigFilter) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{26}
}

func (x *ListFulfillmentReportConfigFilter) GetStatus() ReportStatus {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ReportStatus_REPORT_STATUS_UNSPECIFIED
}

func (x *ListFulfillmentReportConfigFilter) GetCareTypes() []CareType {
	if x != nil {
		return x.CareTypes
	}
	return nil
}

func (x *ListFulfillmentReportConfigFilter) GetStartDate() string {
	if x != nil && x.StartDate != nil {
		return *x.StartDate
	}
	return ""
}

func (x *ListFulfillmentReportConfigFilter) GetEndDate() string {
	if x != nil && x.EndDate != nil {
		return *x.EndDate
	}
	return ""
}

func (x *ListFulfillmentReportConfigFilter) GetPetId() int64 {
	if x != nil && x.PetId != nil {
		return *x.PetId
	}
	return 0
}

// BatchDeleteFulfillmentReportRequest
// (-- api-linter: core::0235::request-names-field=disabled --)
// (-- api-linter: core::0235::request-unknown-fields=disabled --)
type BatchDeleteFulfillmentReportRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// report card ids
	ReportCardIds []int64 `protobuf:"varint,3,rep,packed,name=report_card_ids,json=reportCardIds,proto3" json:"report_card_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchDeleteFulfillmentReportRequest) Reset() {
	*x = BatchDeleteFulfillmentReportRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchDeleteFulfillmentReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDeleteFulfillmentReportRequest) ProtoMessage() {}

func (x *BatchDeleteFulfillmentReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDeleteFulfillmentReportRequest.ProtoReflect.Descriptor instead.
func (*BatchDeleteFulfillmentReportRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{27}
}

func (x *BatchDeleteFulfillmentReportRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *BatchDeleteFulfillmentReportRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BatchDeleteFulfillmentReportRequest) GetReportCardIds() []int64 {
	if x != nil {
		return x.ReportCardIds
	}
	return nil
}

// BatchDeleteFulfillmentReportResponse
// (-- api-linter: core::0235::response-resource-field=disabled --)
type BatchDeleteFulfillmentReportResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchDeleteFulfillmentReportResponse) Reset() {
	*x = BatchDeleteFulfillmentReportResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchDeleteFulfillmentReportResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDeleteFulfillmentReportResponse) ProtoMessage() {}

func (x *BatchDeleteFulfillmentReportResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDeleteFulfillmentReportResponse.ProtoReflect.Descriptor instead.
func (*BatchDeleteFulfillmentReportResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{28}
}

// BatchSendFulfillmentReportRequest
type BatchSendFulfillmentReportRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// report ids
	ReportIds []int64 `protobuf:"varint,3,rep,packed,name=report_ids,json=reportIds,proto3" json:"report_ids,omitempty"`
	// send method
	SendMethod SendMethod `protobuf:"varint,4,opt,name=send_method,json=sendMethod,proto3,enum=backend.proto.fulfillment.v1.SendMethod" json:"send_method,omitempty"`
	// staff id
	StaffId       int64 `protobuf:"varint,5,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchSendFulfillmentReportRequest) Reset() {
	*x = BatchSendFulfillmentReportRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchSendFulfillmentReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchSendFulfillmentReportRequest) ProtoMessage() {}

func (x *BatchSendFulfillmentReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchSendFulfillmentReportRequest.ProtoReflect.Descriptor instead.
func (*BatchSendFulfillmentReportRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{29}
}

func (x *BatchSendFulfillmentReportRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *BatchSendFulfillmentReportRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BatchSendFulfillmentReportRequest) GetReportIds() []int64 {
	if x != nil {
		return x.ReportIds
	}
	return nil
}

func (x *BatchSendFulfillmentReportRequest) GetSendMethod() SendMethod {
	if x != nil {
		return x.SendMethod
	}
	return SendMethod_SEND_METHOD_UNSPECIFIED
}

func (x *BatchSendFulfillmentReportRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// BatchSendFulfillmentReportResponse
type BatchSendFulfillmentReportResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// send results
	SendResults   []*FulfillmentReportSendResult `protobuf:"bytes,1,rep,name=send_results,json=sendResults,proto3" json:"send_results,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchSendFulfillmentReportResponse) Reset() {
	*x = BatchSendFulfillmentReportResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchSendFulfillmentReportResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchSendFulfillmentReportResponse) ProtoMessage() {}

func (x *BatchSendFulfillmentReportResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchSendFulfillmentReportResponse.ProtoReflect.Descriptor instead.
func (*BatchSendFulfillmentReportResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{30}
}

func (x *BatchSendFulfillmentReportResponse) GetSendResults() []*FulfillmentReportSendResult {
	if x != nil {
		return x.SendResults
	}
	return nil
}

// IncreaseFulfillmentOpenedCountRequest
type IncreaseFulfillmentOpenedCountRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// fulfillment report id
	Uuid          string `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IncreaseFulfillmentOpenedCountRequest) Reset() {
	*x = IncreaseFulfillmentOpenedCountRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IncreaseFulfillmentOpenedCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncreaseFulfillmentOpenedCountRequest) ProtoMessage() {}

func (x *IncreaseFulfillmentOpenedCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncreaseFulfillmentOpenedCountRequest.ProtoReflect.Descriptor instead.
func (*IncreaseFulfillmentOpenedCountRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{31}
}

func (x *IncreaseFulfillmentOpenedCountRequest) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

// IncreaseFulfillmentOpenedCountResponse
type IncreaseFulfillmentOpenedCountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IncreaseFulfillmentOpenedCountResponse) Reset() {
	*x = IncreaseFulfillmentOpenedCountResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IncreaseFulfillmentOpenedCountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncreaseFulfillmentOpenedCountResponse) ProtoMessage() {}

func (x *IncreaseFulfillmentOpenedCountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncreaseFulfillmentOpenedCountResponse.ProtoReflect.Descriptor instead.
func (*IncreaseFulfillmentOpenedCountResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{32}
}

// CountFulfillmentReportRequest
type CountFulfillmentReportRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// filter
	Filter        *ListFulfillmentReportConfigFilter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CountFulfillmentReportRequest) Reset() {
	*x = CountFulfillmentReportRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountFulfillmentReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountFulfillmentReportRequest) ProtoMessage() {}

func (x *CountFulfillmentReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountFulfillmentReportRequest.ProtoReflect.Descriptor instead.
func (*CountFulfillmentReportRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{33}
}

func (x *CountFulfillmentReportRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CountFulfillmentReportRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CountFulfillmentReportRequest) GetFilter() *ListFulfillmentReportConfigFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// CountFulfillmentReportResponse
type CountFulfillmentReportResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// total
	Total int32 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	// draft count
	DraftCount int32 `protobuf:"varint,2,opt,name=draft_count,json=draftCount,proto3" json:"draft_count,omitempty"`
	// sent count
	SentCount     int32 `protobuf:"varint,3,opt,name=sent_count,json=sentCount,proto3" json:"sent_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CountFulfillmentReportResponse) Reset() {
	*x = CountFulfillmentReportResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountFulfillmentReportResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountFulfillmentReportResponse) ProtoMessage() {}

func (x *CountFulfillmentReportResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountFulfillmentReportResponse.ProtoReflect.Descriptor instead.
func (*CountFulfillmentReportResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{34}
}

func (x *CountFulfillmentReportResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *CountFulfillmentReportResponse) GetDraftCount() int32 {
	if x != nil {
		return x.DraftCount
	}
	return 0
}

func (x *CountFulfillmentReportResponse) GetSentCount() int32 {
	if x != nil {
		return x.SentCount
	}
	return 0
}

// ------------------------------
// 双写接口相关消息定义
// ------------------------------
// 双写专用的模板数据结构，贴近数据库表结构
type FulfillmentReportTemplateSync struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 主键 id (用于基于ID的更新和删除)
	Id *int64 `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// 公司 id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 业务 id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 护理类型
	CareType CareType `protobuf:"varint,4,opt,name=care_type,json=careType,proto3,enum=backend.proto.fulfillment.v1.CareType" json:"care_type,omitempty"`
	// 感谢信息
	ThankYouMessage string `protobuf:"bytes,5,opt,name=thank_you_message,json=thankYouMessage,proto3" json:"thank_you_message,omitempty"`
	// 主题颜色
	ThemeColor string `protobuf:"bytes,6,opt,name=theme_color,json=themeColor,proto3" json:"theme_color,omitempty"`
	// 浅色主题颜色
	LightThemeColor string `protobuf:"bytes,7,opt,name=light_theme_color,json=lightThemeColor,proto3" json:"light_theme_color,omitempty"`
	// 是否显示展示区域
	ShowShowcase bool `protobuf:"varint,8,opt,name=show_showcase,json=showShowcase,proto3" json:"show_showcase,omitempty"`
	// 是否显示整体反馈
	ShowOverallFeedback bool `protobuf:"varint,9,opt,name=show_overall_feedback,json=showOverallFeedback,proto3" json:"show_overall_feedback,omitempty"`
	// 是否显示宠物状况
	ShowPetCondition bool `protobuf:"varint,10,opt,name=show_pet_condition,json=showPetCondition,proto3" json:"show_pet_condition,omitempty"`
	// 是否显示服务员工姓名
	// (-- api-linter: core::0122::name-suffix=disabled --)
	ShowServiceStaffName bool `protobuf:"varint,11,opt,name=show_service_staff_name,json=showServiceStaffName,proto3" json:"show_service_staff_name,omitempty"`
	// 是否显示下次预约
	ShowNextAppointment bool `protobuf:"varint,12,opt,name=show_next_appointment,json=showNextAppointment,proto3" json:"show_next_appointment,omitempty"`
	// 下次预约日期格式类型
	NextAppointmentDateFormatType NextAppointmentDateFormatType `protobuf:"varint,13,opt,name=next_appointment_date_format_type,json=nextAppointmentDateFormatType,proto3,enum=backend.proto.fulfillment.v1.NextAppointmentDateFormatType" json:"next_appointment_date_format_type,omitempty"`
	// 是否显示评价提升器
	ShowReviewBooster bool `protobuf:"varint,14,opt,name=show_review_booster,json=showReviewBooster,proto3" json:"show_review_booster,omitempty"`
	// 是否显示 Yelp 评价
	ShowYelpReview bool `protobuf:"varint,15,opt,name=show_yelp_review,json=showYelpReview,proto3" json:"show_yelp_review,omitempty"`
	// 是否显示 Google 评价
	ShowGoogleReview bool `protobuf:"varint,16,opt,name=show_google_review,json=showGoogleReview,proto3" json:"show_google_review,omitempty"`
	// 是否显示 Facebook 评价
	ShowFacebookReview bool `protobuf:"varint,17,opt,name=show_facebook_review,json=showFacebookReview,proto3" json:"show_facebook_review,omitempty"`
	// 最后发布时间
	LastPublishTime *timestamppb.Timestamp `protobuf:"bytes,28,opt,name=last_publish_time,json=lastPublishTime,proto3" json:"last_publish_time,omitempty"`
	// 标题
	Title string `protobuf:"bytes,19,opt,name=title,proto3" json:"title,omitempty"`
	// 更新者
	// (-- api-linter: core::0140::prepositions=disabled --)
	UpdateBy int64 `protobuf:"varint,20,opt,name=update_by,json=updateBy,proto3" json:"update_by,omitempty"`
	// 创建时间
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,21,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,22,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// theme code
	ThemeCode     string `protobuf:"bytes,23,opt,name=theme_code,json=themeCode,proto3" json:"theme_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FulfillmentReportTemplateSync) Reset() {
	*x = FulfillmentReportTemplateSync{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FulfillmentReportTemplateSync) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FulfillmentReportTemplateSync) ProtoMessage() {}

func (x *FulfillmentReportTemplateSync) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FulfillmentReportTemplateSync.ProtoReflect.Descriptor instead.
func (*FulfillmentReportTemplateSync) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{35}
}

func (x *FulfillmentReportTemplateSync) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *FulfillmentReportTemplateSync) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *FulfillmentReportTemplateSync) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *FulfillmentReportTemplateSync) GetCareType() CareType {
	if x != nil {
		return x.CareType
	}
	return CareType_CARE_TYPE_UNSPECIFIED
}

func (x *FulfillmentReportTemplateSync) GetThankYouMessage() string {
	if x != nil {
		return x.ThankYouMessage
	}
	return ""
}

func (x *FulfillmentReportTemplateSync) GetThemeColor() string {
	if x != nil {
		return x.ThemeColor
	}
	return ""
}

func (x *FulfillmentReportTemplateSync) GetLightThemeColor() string {
	if x != nil {
		return x.LightThemeColor
	}
	return ""
}

func (x *FulfillmentReportTemplateSync) GetShowShowcase() bool {
	if x != nil {
		return x.ShowShowcase
	}
	return false
}

func (x *FulfillmentReportTemplateSync) GetShowOverallFeedback() bool {
	if x != nil {
		return x.ShowOverallFeedback
	}
	return false
}

func (x *FulfillmentReportTemplateSync) GetShowPetCondition() bool {
	if x != nil {
		return x.ShowPetCondition
	}
	return false
}

func (x *FulfillmentReportTemplateSync) GetShowServiceStaffName() bool {
	if x != nil {
		return x.ShowServiceStaffName
	}
	return false
}

func (x *FulfillmentReportTemplateSync) GetShowNextAppointment() bool {
	if x != nil {
		return x.ShowNextAppointment
	}
	return false
}

func (x *FulfillmentReportTemplateSync) GetNextAppointmentDateFormatType() NextAppointmentDateFormatType {
	if x != nil {
		return x.NextAppointmentDateFormatType
	}
	return NextAppointmentDateFormatType_NEXT_APPOINTMENT_DATE_FORMAT_TYPE_UNSPECIFIED
}

func (x *FulfillmentReportTemplateSync) GetShowReviewBooster() bool {
	if x != nil {
		return x.ShowReviewBooster
	}
	return false
}

func (x *FulfillmentReportTemplateSync) GetShowYelpReview() bool {
	if x != nil {
		return x.ShowYelpReview
	}
	return false
}

func (x *FulfillmentReportTemplateSync) GetShowGoogleReview() bool {
	if x != nil {
		return x.ShowGoogleReview
	}
	return false
}

func (x *FulfillmentReportTemplateSync) GetShowFacebookReview() bool {
	if x != nil {
		return x.ShowFacebookReview
	}
	return false
}

func (x *FulfillmentReportTemplateSync) GetLastPublishTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastPublishTime
	}
	return nil
}

func (x *FulfillmentReportTemplateSync) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *FulfillmentReportTemplateSync) GetUpdateBy() int64 {
	if x != nil {
		return x.UpdateBy
	}
	return 0
}

func (x *FulfillmentReportTemplateSync) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *FulfillmentReportTemplateSync) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *FulfillmentReportTemplateSync) GetThemeCode() string {
	if x != nil {
		return x.ThemeCode
	}
	return ""
}

// 模板唯一键标识
type FulfillmentReportTemplateUniqueKey struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司 id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 业务 id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 护理类型
	CareType      CareType `protobuf:"varint,3,opt,name=care_type,json=careType,proto3,enum=backend.proto.fulfillment.v1.CareType" json:"care_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FulfillmentReportTemplateUniqueKey) Reset() {
	*x = FulfillmentReportTemplateUniqueKey{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FulfillmentReportTemplateUniqueKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FulfillmentReportTemplateUniqueKey) ProtoMessage() {}

func (x *FulfillmentReportTemplateUniqueKey) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FulfillmentReportTemplateUniqueKey.ProtoReflect.Descriptor instead.
func (*FulfillmentReportTemplateUniqueKey) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{36}
}

func (x *FulfillmentReportTemplateUniqueKey) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *FulfillmentReportTemplateUniqueKey) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *FulfillmentReportTemplateUniqueKey) GetCareType() CareType {
	if x != nil {
		return x.CareType
	}
	return CareType_CARE_TYPE_UNSPECIFIED
}

// 同步履约报告模板请求
type SyncFulfillmentReportTemplateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 操作类型：CREATE, UPDATE, UPSERT, DELETE
	Operation SyncOperation `protobuf:"varint,1,opt,name=operation,proto3,enum=backend.proto.fulfillment.v1.SyncOperation" json:"operation,omitempty"`
	// 定位方式：通过ID或唯一键
	//
	// Types that are valid to be assigned to Identifier:
	//
	//	*SyncFulfillmentReportTemplateRequest_TemplateId
	//	*SyncFulfillmentReportTemplateRequest_UniqueKey
	Identifier isSyncFulfillmentReportTemplateRequest_Identifier `protobuf_oneof:"identifier"`
	// 模板数据（CREATE/UPDATE/UPSERT 时必填，DELETE 时可选）
	Template      *FulfillmentReportTemplateSync `protobuf:"bytes,4,opt,name=template,proto3,oneof" json:"template,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncFulfillmentReportTemplateRequest) Reset() {
	*x = SyncFulfillmentReportTemplateRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncFulfillmentReportTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncFulfillmentReportTemplateRequest) ProtoMessage() {}

func (x *SyncFulfillmentReportTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncFulfillmentReportTemplateRequest.ProtoReflect.Descriptor instead.
func (*SyncFulfillmentReportTemplateRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{37}
}

func (x *SyncFulfillmentReportTemplateRequest) GetOperation() SyncOperation {
	if x != nil {
		return x.Operation
	}
	return SyncOperation_SYNC_OPERATION_UNSPECIFIED
}

func (x *SyncFulfillmentReportTemplateRequest) GetIdentifier() isSyncFulfillmentReportTemplateRequest_Identifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

func (x *SyncFulfillmentReportTemplateRequest) GetTemplateId() int64 {
	if x != nil {
		if x, ok := x.Identifier.(*SyncFulfillmentReportTemplateRequest_TemplateId); ok {
			return x.TemplateId
		}
	}
	return 0
}

func (x *SyncFulfillmentReportTemplateRequest) GetUniqueKey() *FulfillmentReportTemplateUniqueKey {
	if x != nil {
		if x, ok := x.Identifier.(*SyncFulfillmentReportTemplateRequest_UniqueKey); ok {
			return x.UniqueKey
		}
	}
	return nil
}

func (x *SyncFulfillmentReportTemplateRequest) GetTemplate() *FulfillmentReportTemplateSync {
	if x != nil {
		return x.Template
	}
	return nil
}

type isSyncFulfillmentReportTemplateRequest_Identifier interface {
	isSyncFulfillmentReportTemplateRequest_Identifier()
}

type SyncFulfillmentReportTemplateRequest_TemplateId struct {
	// 通过ID定位（适用于已知目标系统记录ID的场景）
	TemplateId int64 `protobuf:"varint,2,opt,name=template_id,json=templateId,proto3,oneof"`
}

type SyncFulfillmentReportTemplateRequest_UniqueKey struct {
	// 通过唯一键定位（适用于基于业务语义的双写场景）
	UniqueKey *FulfillmentReportTemplateUniqueKey `protobuf:"bytes,3,opt,name=unique_key,json=uniqueKey,proto3,oneof"`
}

func (*SyncFulfillmentReportTemplateRequest_TemplateId) isSyncFulfillmentReportTemplateRequest_Identifier() {
}

func (*SyncFulfillmentReportTemplateRequest_UniqueKey) isSyncFulfillmentReportTemplateRequest_Identifier() {
}

// 同步履约报告模板响应
type SyncFulfillmentReportTemplateResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 同步后的模板 ID
	TemplateId int64 `protobuf:"varint,1,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	// 同步状态
	Status SyncStatus `protobuf:"varint,2,opt,name=status,proto3,enum=backend.proto.fulfillment.v1.SyncStatus" json:"status,omitempty"`
	// 错误消息（如果有）
	ErrorMessage  string `protobuf:"bytes,3,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncFulfillmentReportTemplateResponse) Reset() {
	*x = SyncFulfillmentReportTemplateResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncFulfillmentReportTemplateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncFulfillmentReportTemplateResponse) ProtoMessage() {}

func (x *SyncFulfillmentReportTemplateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncFulfillmentReportTemplateResponse.ProtoReflect.Descriptor instead.
func (*SyncFulfillmentReportTemplateResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{38}
}

func (x *SyncFulfillmentReportTemplateResponse) GetTemplateId() int64 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *SyncFulfillmentReportTemplateResponse) GetStatus() SyncStatus {
	if x != nil {
		return x.Status
	}
	return SyncStatus_SYNC_STATUS_UNSPECIFIED
}

func (x *SyncFulfillmentReportTemplateResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

// 双写专用的问题数据结构，贴近数据库表结构
type FulfillmentReportQuestionSync struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 主键 id (用于基于ID的更新和删除)
	Id *int64 `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// 公司 id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 业务 id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// care type
	CareType CareType `protobuf:"varint,4,opt,name=care_type,json=careType,proto3,enum=backend.proto.fulfillment.v1.CareType" json:"care_type,omitempty"`
	// 问题类别
	Category QuestionCategory `protobuf:"varint,5,opt,name=category,proto3,enum=backend.proto.fulfillment.v1.QuestionCategory" json:"category,omitempty"`
	// 问题类型
	Type QuestionType `protobuf:"varint,6,opt,name=type,proto3,enum=backend.proto.fulfillment.v1.QuestionType" json:"type,omitempty"`
	// 问题唯一键
	Key string `protobuf:"bytes,7,opt,name=key,proto3" json:"key,omitempty"`
	// 问题标题
	Title string `protobuf:"bytes,8,opt,name=title,proto3" json:"title,omitempty"`
	// 扩展配置 JSON
	ExtraJson string `protobuf:"bytes,9,opt,name=extra_json,json=extraJson,proto3" json:"extra_json,omitempty"`
	// 是否默认问题
	IsDefault bool `protobuf:"varint,10,opt,name=is_default,json=isDefault,proto3" json:"is_default,omitempty"`
	// 是否必填
	IsRequired bool `protobuf:"varint,11,opt,name=is_required,json=isRequired,proto3" json:"is_required,omitempty"`
	// 类型是否可编辑
	IsTypeEditable bool `protobuf:"varint,12,opt,name=is_type_editable,json=isTypeEditable,proto3" json:"is_type_editable,omitempty"`
	// 标题是否可编辑
	IsTitleEditable bool `protobuf:"varint,13,opt,name=is_title_editable,json=isTitleEditable,proto3" json:"is_title_editable,omitempty"`
	// 选项是否可编辑
	IsOptionsEditable bool `protobuf:"varint,14,opt,name=is_options_editable,json=isOptionsEditable,proto3" json:"is_options_editable,omitempty"`
	// 排序值
	Sort int32 `protobuf:"varint,15,opt,name=sort,proto3" json:"sort,omitempty"`
	// 创建时间
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间
	UpdateTime    *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FulfillmentReportQuestionSync) Reset() {
	*x = FulfillmentReportQuestionSync{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FulfillmentReportQuestionSync) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FulfillmentReportQuestionSync) ProtoMessage() {}

func (x *FulfillmentReportQuestionSync) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FulfillmentReportQuestionSync.ProtoReflect.Descriptor instead.
func (*FulfillmentReportQuestionSync) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{39}
}

func (x *FulfillmentReportQuestionSync) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *FulfillmentReportQuestionSync) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *FulfillmentReportQuestionSync) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *FulfillmentReportQuestionSync) GetCareType() CareType {
	if x != nil {
		return x.CareType
	}
	return CareType_CARE_TYPE_UNSPECIFIED
}

func (x *FulfillmentReportQuestionSync) GetCategory() QuestionCategory {
	if x != nil {
		return x.Category
	}
	return QuestionCategory_QUESTION_CATEGORY_UNSPECIFIED
}

func (x *FulfillmentReportQuestionSync) GetType() QuestionType {
	if x != nil {
		return x.Type
	}
	return QuestionType_QUESTION_TYPE_UNSPECIFIED
}

func (x *FulfillmentReportQuestionSync) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *FulfillmentReportQuestionSync) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *FulfillmentReportQuestionSync) GetExtraJson() string {
	if x != nil {
		return x.ExtraJson
	}
	return ""
}

func (x *FulfillmentReportQuestionSync) GetIsDefault() bool {
	if x != nil {
		return x.IsDefault
	}
	return false
}

func (x *FulfillmentReportQuestionSync) GetIsRequired() bool {
	if x != nil {
		return x.IsRequired
	}
	return false
}

func (x *FulfillmentReportQuestionSync) GetIsTypeEditable() bool {
	if x != nil {
		return x.IsTypeEditable
	}
	return false
}

func (x *FulfillmentReportQuestionSync) GetIsTitleEditable() bool {
	if x != nil {
		return x.IsTitleEditable
	}
	return false
}

func (x *FulfillmentReportQuestionSync) GetIsOptionsEditable() bool {
	if x != nil {
		return x.IsOptionsEditable
	}
	return false
}

func (x *FulfillmentReportQuestionSync) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *FulfillmentReportQuestionSync) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *FulfillmentReportQuestionSync) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// 问题模板标识符
type QuestionTemplateIdentifier struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 定位方式：通过template_id或唯一键
	//
	// Types that are valid to be assigned to Identifier:
	//
	//	*QuestionTemplateIdentifier_TemplateId
	//	*QuestionTemplateIdentifier_TemplateUniqueKey
	Identifier    isQuestionTemplateIdentifier_Identifier `protobuf_oneof:"identifier"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QuestionTemplateIdentifier) Reset() {
	*x = QuestionTemplateIdentifier{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuestionTemplateIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestionTemplateIdentifier) ProtoMessage() {}

func (x *QuestionTemplateIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestionTemplateIdentifier.ProtoReflect.Descriptor instead.
func (*QuestionTemplateIdentifier) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{40}
}

func (x *QuestionTemplateIdentifier) GetIdentifier() isQuestionTemplateIdentifier_Identifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

func (x *QuestionTemplateIdentifier) GetTemplateId() int64 {
	if x != nil {
		if x, ok := x.Identifier.(*QuestionTemplateIdentifier_TemplateId); ok {
			return x.TemplateId
		}
	}
	return 0
}

func (x *QuestionTemplateIdentifier) GetTemplateUniqueKey() *FulfillmentReportTemplateUniqueKey {
	if x != nil {
		if x, ok := x.Identifier.(*QuestionTemplateIdentifier_TemplateUniqueKey); ok {
			return x.TemplateUniqueKey
		}
	}
	return nil
}

type isQuestionTemplateIdentifier_Identifier interface {
	isQuestionTemplateIdentifier_Identifier()
}

type QuestionTemplateIdentifier_TemplateId struct {
	// 通过模板ID定位
	TemplateId int64 `protobuf:"varint,1,opt,name=template_id,json=templateId,proto3,oneof"`
}

type QuestionTemplateIdentifier_TemplateUniqueKey struct {
	// 通过模板唯一键定位
	TemplateUniqueKey *FulfillmentReportTemplateUniqueKey `protobuf:"bytes,2,opt,name=template_unique_key,json=templateUniqueKey,proto3,oneof"`
}

func (*QuestionTemplateIdentifier_TemplateId) isQuestionTemplateIdentifier_Identifier() {}

func (*QuestionTemplateIdentifier_TemplateUniqueKey) isQuestionTemplateIdentifier_Identifier() {}

// 批量同步履约报告问题请求
type BatchSyncFulfillmentReportQuestionsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 操作类型：CREATE, UPDATE, UPSERT, DELETE
	Operation SyncOperation `protobuf:"varint,1,opt,name=operation,proto3,enum=backend.proto.fulfillment.v1.SyncOperation" json:"operation,omitempty"`
	// 模板标识符（定位所属模板）
	TemplateIdentifier *QuestionTemplateIdentifier `protobuf:"bytes,2,opt,name=template_identifier,json=templateIdentifier,proto3" json:"template_identifier,omitempty"`
	// 问题数据列表
	Questions     []*FulfillmentReportQuestionSync `protobuf:"bytes,3,rep,name=questions,proto3" json:"questions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchSyncFulfillmentReportQuestionsRequest) Reset() {
	*x = BatchSyncFulfillmentReportQuestionsRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchSyncFulfillmentReportQuestionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchSyncFulfillmentReportQuestionsRequest) ProtoMessage() {}

func (x *BatchSyncFulfillmentReportQuestionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchSyncFulfillmentReportQuestionsRequest.ProtoReflect.Descriptor instead.
func (*BatchSyncFulfillmentReportQuestionsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{41}
}

func (x *BatchSyncFulfillmentReportQuestionsRequest) GetOperation() SyncOperation {
	if x != nil {
		return x.Operation
	}
	return SyncOperation_SYNC_OPERATION_UNSPECIFIED
}

func (x *BatchSyncFulfillmentReportQuestionsRequest) GetTemplateIdentifier() *QuestionTemplateIdentifier {
	if x != nil {
		return x.TemplateIdentifier
	}
	return nil
}

func (x *BatchSyncFulfillmentReportQuestionsRequest) GetQuestions() []*FulfillmentReportQuestionSync {
	if x != nil {
		return x.Questions
	}
	return nil
}

// 批量同步履约报告问题响应
type BatchSyncFulfillmentReportQuestionsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 同步状态
	Status SyncStatus `protobuf:"varint,2,opt,name=status,proto3,enum=backend.proto.fulfillment.v1.SyncStatus" json:"status,omitempty"`
	// 错误消息（如果有）
	ErrorMessage string `protobuf:"bytes,3,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	// 关联的模板ID
	TemplateId    int64 `protobuf:"varint,4,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchSyncFulfillmentReportQuestionsResponse) Reset() {
	*x = BatchSyncFulfillmentReportQuestionsResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchSyncFulfillmentReportQuestionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchSyncFulfillmentReportQuestionsResponse) ProtoMessage() {}

func (x *BatchSyncFulfillmentReportQuestionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchSyncFulfillmentReportQuestionsResponse.ProtoReflect.Descriptor instead.
func (*BatchSyncFulfillmentReportQuestionsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{42}
}

func (x *BatchSyncFulfillmentReportQuestionsResponse) GetStatus() SyncStatus {
	if x != nil {
		return x.Status
	}
	return SyncStatus_SYNC_STATUS_UNSPECIFIED
}

func (x *BatchSyncFulfillmentReportQuestionsResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *BatchSyncFulfillmentReportQuestionsResponse) GetTemplateId() int64 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

// 双写专用的履约报告数据结构，贴近数据库表结构
type FulfillmentReportSync struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 主键 id (用于基于ID的更新和删除)
	Id *int64 `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// 公司 id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 业务 id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 客户 id
	CustomerId int64 `protobuf:"varint,4,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 预约 id
	AppointmentId int64 `protobuf:"varint,5,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// 护理类型
	CareType CareType `protobuf:"varint,6,opt,name=care_type,json=careType,proto3,enum=backend.proto.fulfillment.v1.CareType" json:"care_type,omitempty"`
	// 宠物 id
	PetId int64 `protobuf:"varint,7,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet type id
	PetTypeId int64 `protobuf:"varint,8,opt,name=pet_type_id,json=petTypeId,proto3" json:"pet_type_id,omitempty"`
	// 唯一标识符
	Uuid string `protobuf:"bytes,9,opt,name=uuid,proto3" json:"uuid,omitempty"`
	// 模板版本
	// (-- api-linter: core::0142::time-field-names=disabled --)
	TemplateVersion *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=template_version,json=templateVersion,proto3" json:"template_version,omitempty"`
	// 模板JSON数据
	TemplateJson string `protobuf:"bytes,11,opt,name=template_json,json=templateJson,proto3" json:"template_json,omitempty"`
	// 内容JSON数据
	ContentJson string `protobuf:"bytes,12,opt,name=content_json,json=contentJson,proto3" json:"content_json,omitempty"`
	// 报告状态
	Status string `protobuf:"bytes,13,opt,name=status,proto3" json:"status,omitempty"`
	// 链接打开次数
	LinkOpenedCount int32 `protobuf:"varint,14,opt,name=link_opened_count,json=linkOpenedCount,proto3" json:"link_opened_count,omitempty"`
	// 服务日期
	// (-- api-linter: core::0142::time-field-type=disabled --)
	ServiceDate string `protobuf:"bytes,15,opt,name=service_date,json=serviceDate,proto3" json:"service_date,omitempty"`
	// 更新者
	// (-- api-linter: core::0140::prepositions=disabled --)
	UpdateBy int64 `protobuf:"varint,16,opt,name=update_by,json=updateBy,proto3" json:"update_by,omitempty"`
	// 创建时间
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// theme code
	ThemeCode     string `protobuf:"bytes,19,opt,name=theme_code,json=themeCode,proto3" json:"theme_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FulfillmentReportSync) Reset() {
	*x = FulfillmentReportSync{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FulfillmentReportSync) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FulfillmentReportSync) ProtoMessage() {}

func (x *FulfillmentReportSync) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FulfillmentReportSync.ProtoReflect.Descriptor instead.
func (*FulfillmentReportSync) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{43}
}

func (x *FulfillmentReportSync) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *FulfillmentReportSync) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *FulfillmentReportSync) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *FulfillmentReportSync) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *FulfillmentReportSync) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *FulfillmentReportSync) GetCareType() CareType {
	if x != nil {
		return x.CareType
	}
	return CareType_CARE_TYPE_UNSPECIFIED
}

func (x *FulfillmentReportSync) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *FulfillmentReportSync) GetPetTypeId() int64 {
	if x != nil {
		return x.PetTypeId
	}
	return 0
}

func (x *FulfillmentReportSync) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *FulfillmentReportSync) GetTemplateVersion() *timestamppb.Timestamp {
	if x != nil {
		return x.TemplateVersion
	}
	return nil
}

func (x *FulfillmentReportSync) GetTemplateJson() string {
	if x != nil {
		return x.TemplateJson
	}
	return ""
}

func (x *FulfillmentReportSync) GetContentJson() string {
	if x != nil {
		return x.ContentJson
	}
	return ""
}

func (x *FulfillmentReportSync) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *FulfillmentReportSync) GetLinkOpenedCount() int32 {
	if x != nil {
		return x.LinkOpenedCount
	}
	return 0
}

func (x *FulfillmentReportSync) GetServiceDate() string {
	if x != nil {
		return x.ServiceDate
	}
	return ""
}

func (x *FulfillmentReportSync) GetUpdateBy() int64 {
	if x != nil {
		return x.UpdateBy
	}
	return 0
}

func (x *FulfillmentReportSync) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *FulfillmentReportSync) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *FulfillmentReportSync) GetThemeCode() string {
	if x != nil {
		return x.ThemeCode
	}
	return ""
}

// 履约报告唯一键标识
type FulfillmentReportUniqueKey struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 业务 id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 预约 id
	AppointmentId int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// 宠物 id
	PetId int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// 护理类型
	CareType CareType `protobuf:"varint,4,opt,name=care_type,json=careType,proto3,enum=backend.proto.fulfillment.v1.CareType" json:"care_type,omitempty"`
	// 服务日期
	// (-- api-linter: core::0142::time-field-type=disabled --)
	ServiceDate   string `protobuf:"bytes,5,opt,name=service_date,json=serviceDate,proto3" json:"service_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FulfillmentReportUniqueKey) Reset() {
	*x = FulfillmentReportUniqueKey{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FulfillmentReportUniqueKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FulfillmentReportUniqueKey) ProtoMessage() {}

func (x *FulfillmentReportUniqueKey) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FulfillmentReportUniqueKey.ProtoReflect.Descriptor instead.
func (*FulfillmentReportUniqueKey) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{44}
}

func (x *FulfillmentReportUniqueKey) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *FulfillmentReportUniqueKey) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *FulfillmentReportUniqueKey) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *FulfillmentReportUniqueKey) GetCareType() CareType {
	if x != nil {
		return x.CareType
	}
	return CareType_CARE_TYPE_UNSPECIFIED
}

func (x *FulfillmentReportUniqueKey) GetServiceDate() string {
	if x != nil {
		return x.ServiceDate
	}
	return ""
}

// 同步履约报告请求
type SyncFulfillmentReportRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 操作类型：CREATE, UPDATE, UPSERT, DELETE
	Operation SyncOperation `protobuf:"varint,1,opt,name=operation,proto3,enum=backend.proto.fulfillment.v1.SyncOperation" json:"operation,omitempty"`
	// 定位方式：通过ID或唯一键
	//
	// Types that are valid to be assigned to Identifier:
	//
	//	*SyncFulfillmentReportRequest_ReportId
	//	*SyncFulfillmentReportRequest_UniqueKey
	Identifier isSyncFulfillmentReportRequest_Identifier `protobuf_oneof:"identifier"`
	// 报告数据（CREATE/UPDATE/UPSERT 时必填，DELETE 时可选）
	Report        *FulfillmentReportSync `protobuf:"bytes,4,opt,name=report,proto3,oneof" json:"report,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncFulfillmentReportRequest) Reset() {
	*x = SyncFulfillmentReportRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncFulfillmentReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncFulfillmentReportRequest) ProtoMessage() {}

func (x *SyncFulfillmentReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncFulfillmentReportRequest.ProtoReflect.Descriptor instead.
func (*SyncFulfillmentReportRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{45}
}

func (x *SyncFulfillmentReportRequest) GetOperation() SyncOperation {
	if x != nil {
		return x.Operation
	}
	return SyncOperation_SYNC_OPERATION_UNSPECIFIED
}

func (x *SyncFulfillmentReportRequest) GetIdentifier() isSyncFulfillmentReportRequest_Identifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

func (x *SyncFulfillmentReportRequest) GetReportId() int64 {
	if x != nil {
		if x, ok := x.Identifier.(*SyncFulfillmentReportRequest_ReportId); ok {
			return x.ReportId
		}
	}
	return 0
}

func (x *SyncFulfillmentReportRequest) GetUniqueKey() *FulfillmentReportUniqueKey {
	if x != nil {
		if x, ok := x.Identifier.(*SyncFulfillmentReportRequest_UniqueKey); ok {
			return x.UniqueKey
		}
	}
	return nil
}

func (x *SyncFulfillmentReportRequest) GetReport() *FulfillmentReportSync {
	if x != nil {
		return x.Report
	}
	return nil
}

type isSyncFulfillmentReportRequest_Identifier interface {
	isSyncFulfillmentReportRequest_Identifier()
}

type SyncFulfillmentReportRequest_ReportId struct {
	// 通过ID定位（适用于已知目标系统记录ID的场景）
	ReportId int64 `protobuf:"varint,2,opt,name=report_id,json=reportId,proto3,oneof"`
}

type SyncFulfillmentReportRequest_UniqueKey struct {
	// 通过唯一键定位（适用于基于业务语义的双写场景）
	UniqueKey *FulfillmentReportUniqueKey `protobuf:"bytes,3,opt,name=unique_key,json=uniqueKey,proto3,oneof"`
}

func (*SyncFulfillmentReportRequest_ReportId) isSyncFulfillmentReportRequest_Identifier() {}

func (*SyncFulfillmentReportRequest_UniqueKey) isSyncFulfillmentReportRequest_Identifier() {}

// 同步履约报告响应
type SyncFulfillmentReportResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 同步后的报告 ID
	ReportId int64 `protobuf:"varint,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`
	// 同步状态
	Status SyncStatus `protobuf:"varint,2,opt,name=status,proto3,enum=backend.proto.fulfillment.v1.SyncStatus" json:"status,omitempty"`
	// 错误消息（如果有）
	ErrorMessage  string `protobuf:"bytes,3,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncFulfillmentReportResponse) Reset() {
	*x = SyncFulfillmentReportResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncFulfillmentReportResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncFulfillmentReportResponse) ProtoMessage() {}

func (x *SyncFulfillmentReportResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncFulfillmentReportResponse.ProtoReflect.Descriptor instead.
func (*SyncFulfillmentReportResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{46}
}

func (x *SyncFulfillmentReportResponse) GetReportId() int64 {
	if x != nil {
		return x.ReportId
	}
	return 0
}

func (x *SyncFulfillmentReportResponse) GetStatus() SyncStatus {
	if x != nil {
		return x.Status
	}
	return SyncStatus_SYNC_STATUS_UNSPECIFIED
}

func (x *SyncFulfillmentReportResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

// 双写专用的发送记录数据结构，贴近数据库表结构
type FulfillmentReportSendRecordSync struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 主键 id (用于基于ID的更新和删除)
	Id *int64 `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// 报告 id
	ReportId int64 `protobuf:"varint,2,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`
	// 公司 id
	CompanyId int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 业务 id
	BusinessId int64 `protobuf:"varint,4,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 预约 id
	AppointmentId int64 `protobuf:"varint,5,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// 内容JSON数据
	ContentJson string `protobuf:"bytes,6,opt,name=content_json,json=contentJson,proto3" json:"content_json,omitempty"`
	// 宠物 id
	PetId int64 `protobuf:"varint,7,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// 发送方式
	SendMethod SendMethod `protobuf:"varint,8,opt,name=send_method,json=sendMethod,proto3,enum=backend.proto.fulfillment.v1.SendMethod" json:"send_method,omitempty"`
	// 发送时间
	SentTime *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=sent_time,json=sentTime,proto3" json:"sent_time,omitempty"`
	// 发送者
	// (-- api-linter: core::0140::prepositions=disabled --)
	SentBy int64 `protobuf:"varint,10,opt,name=sent_by,json=sentBy,proto3" json:"sent_by,omitempty"`
	// 错误消息
	ErrorMessage string `protobuf:"bytes,11,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	// 是否发送成功
	IsSentSuccess bool `protobuf:"varint,12,opt,name=is_sent_success,json=isSentSuccess,proto3" json:"is_sent_success,omitempty"`
	// 创建时间
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间
	UpdateTime    *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FulfillmentReportSendRecordSync) Reset() {
	*x = FulfillmentReportSendRecordSync{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FulfillmentReportSendRecordSync) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FulfillmentReportSendRecordSync) ProtoMessage() {}

func (x *FulfillmentReportSendRecordSync) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FulfillmentReportSendRecordSync.ProtoReflect.Descriptor instead.
func (*FulfillmentReportSendRecordSync) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{47}
}

func (x *FulfillmentReportSendRecordSync) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *FulfillmentReportSendRecordSync) GetReportId() int64 {
	if x != nil {
		return x.ReportId
	}
	return 0
}

func (x *FulfillmentReportSendRecordSync) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *FulfillmentReportSendRecordSync) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *FulfillmentReportSendRecordSync) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *FulfillmentReportSendRecordSync) GetContentJson() string {
	if x != nil {
		return x.ContentJson
	}
	return ""
}

func (x *FulfillmentReportSendRecordSync) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *FulfillmentReportSendRecordSync) GetSendMethod() SendMethod {
	if x != nil {
		return x.SendMethod
	}
	return SendMethod_SEND_METHOD_UNSPECIFIED
}

func (x *FulfillmentReportSendRecordSync) GetSentTime() *timestamppb.Timestamp {
	if x != nil {
		return x.SentTime
	}
	return nil
}

func (x *FulfillmentReportSendRecordSync) GetSentBy() int64 {
	if x != nil {
		return x.SentBy
	}
	return 0
}

func (x *FulfillmentReportSendRecordSync) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *FulfillmentReportSendRecordSync) GetIsSentSuccess() bool {
	if x != nil {
		return x.IsSentSuccess
	}
	return false
}

func (x *FulfillmentReportSendRecordSync) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *FulfillmentReportSendRecordSync) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// 发送记录标识符
type SendRecordIdentifier struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 发送方式（必须）
	SendMethod SendMethod `protobuf:"varint,1,opt,name=send_method,json=sendMethod,proto3,enum=backend.proto.fulfillment.v1.SendMethod" json:"send_method,omitempty"`
	// 报告唯一键（必须）
	ReportUniqueKey *FulfillmentReportUniqueKey `protobuf:"bytes,2,opt,name=report_unique_key,json=reportUniqueKey,proto3" json:"report_unique_key,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *SendRecordIdentifier) Reset() {
	*x = SendRecordIdentifier{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendRecordIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendRecordIdentifier) ProtoMessage() {}

func (x *SendRecordIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendRecordIdentifier.ProtoReflect.Descriptor instead.
func (*SendRecordIdentifier) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{48}
}

func (x *SendRecordIdentifier) GetSendMethod() SendMethod {
	if x != nil {
		return x.SendMethod
	}
	return SendMethod_SEND_METHOD_UNSPECIFIED
}

func (x *SendRecordIdentifier) GetReportUniqueKey() *FulfillmentReportUniqueKey {
	if x != nil {
		return x.ReportUniqueKey
	}
	return nil
}

// 同步履约报告发送记录请求
type SyncFulfillmentReportSendRecordRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 操作类型：CREATE, UPDATE, UPSERT, DELETE
	Operation SyncOperation `protobuf:"varint,1,opt,name=operation,proto3,enum=backend.proto.fulfillment.v1.SyncOperation" json:"operation,omitempty"`
	// 定位方式：通过记录ID或业务标识符
	//
	// Types that are valid to be assigned to Identifier:
	//
	//	*SyncFulfillmentReportSendRecordRequest_RecordId
	//	*SyncFulfillmentReportSendRecordRequest_BusinessIdentifier
	Identifier isSyncFulfillmentReportSendRecordRequest_Identifier `protobuf_oneof:"identifier"`
	// 发送记录数据（CREATE/UPDATE/UPSERT 时必填，DELETE 时可选）
	SendRecord    *FulfillmentReportSendRecordSync `protobuf:"bytes,4,opt,name=send_record,json=sendRecord,proto3,oneof" json:"send_record,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncFulfillmentReportSendRecordRequest) Reset() {
	*x = SyncFulfillmentReportSendRecordRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncFulfillmentReportSendRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncFulfillmentReportSendRecordRequest) ProtoMessage() {}

func (x *SyncFulfillmentReportSendRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncFulfillmentReportSendRecordRequest.ProtoReflect.Descriptor instead.
func (*SyncFulfillmentReportSendRecordRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{49}
}

func (x *SyncFulfillmentReportSendRecordRequest) GetOperation() SyncOperation {
	if x != nil {
		return x.Operation
	}
	return SyncOperation_SYNC_OPERATION_UNSPECIFIED
}

func (x *SyncFulfillmentReportSendRecordRequest) GetIdentifier() isSyncFulfillmentReportSendRecordRequest_Identifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

func (x *SyncFulfillmentReportSendRecordRequest) GetRecordId() int64 {
	if x != nil {
		if x, ok := x.Identifier.(*SyncFulfillmentReportSendRecordRequest_RecordId); ok {
			return x.RecordId
		}
	}
	return 0
}

func (x *SyncFulfillmentReportSendRecordRequest) GetBusinessIdentifier() *SendRecordIdentifier {
	if x != nil {
		if x, ok := x.Identifier.(*SyncFulfillmentReportSendRecordRequest_BusinessIdentifier); ok {
			return x.BusinessIdentifier
		}
	}
	return nil
}

func (x *SyncFulfillmentReportSendRecordRequest) GetSendRecord() *FulfillmentReportSendRecordSync {
	if x != nil {
		return x.SendRecord
	}
	return nil
}

type isSyncFulfillmentReportSendRecordRequest_Identifier interface {
	isSyncFulfillmentReportSendRecordRequest_Identifier()
}

type SyncFulfillmentReportSendRecordRequest_RecordId struct {
	// 通过记录ID定位（适用于已知记录ID的场景）
	RecordId int64 `protobuf:"varint,2,opt,name=record_id,json=recordId,proto3,oneof"`
}

type SyncFulfillmentReportSendRecordRequest_BusinessIdentifier struct {
	// 通过业务标识符定位（适用于基于业务语义的双写场景）
	BusinessIdentifier *SendRecordIdentifier `protobuf:"bytes,3,opt,name=business_identifier,json=businessIdentifier,proto3,oneof"`
}

func (*SyncFulfillmentReportSendRecordRequest_RecordId) isSyncFulfillmentReportSendRecordRequest_Identifier() {
}

func (*SyncFulfillmentReportSendRecordRequest_BusinessIdentifier) isSyncFulfillmentReportSendRecordRequest_Identifier() {
}

// 同步履约报告发送记录响应
type SyncFulfillmentReportSendRecordResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 同步后的记录 ID
	RecordId int64 `protobuf:"varint,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	// 关联的报告 ID
	ReportId int64 `protobuf:"varint,2,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`
	// 同步状态
	Status SyncStatus `protobuf:"varint,3,opt,name=status,proto3,enum=backend.proto.fulfillment.v1.SyncStatus" json:"status,omitempty"`
	// 错误消息（如果有）
	ErrorMessage  string `protobuf:"bytes,4,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncFulfillmentReportSendRecordResponse) Reset() {
	*x = SyncFulfillmentReportSendRecordResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncFulfillmentReportSendRecordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncFulfillmentReportSendRecordResponse) ProtoMessage() {}

func (x *SyncFulfillmentReportSendRecordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncFulfillmentReportSendRecordResponse.ProtoReflect.Descriptor instead.
func (*SyncFulfillmentReportSendRecordResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{50}
}

func (x *SyncFulfillmentReportSendRecordResponse) GetRecordId() int64 {
	if x != nil {
		return x.RecordId
	}
	return 0
}

func (x *SyncFulfillmentReportSendRecordResponse) GetReportId() int64 {
	if x != nil {
		return x.ReportId
	}
	return 0
}

func (x *SyncFulfillmentReportSendRecordResponse) GetStatus() SyncStatus {
	if x != nil {
		return x.Status
	}
	return SyncStatus_SYNC_STATUS_UNSPECIFIED
}

func (x *SyncFulfillmentReportSendRecordResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

// ------------------------------
// 批量迁移接口相关消息定义
// ------------------------------
// 批量迁移模板请求
type BatchMigrateTemplatesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 模板数据列表（复用双写接口结构）
	Templates     []*FulfillmentReportTemplateSync `protobuf:"bytes,1,rep,name=templates,proto3" json:"templates,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchMigrateTemplatesRequest) Reset() {
	*x = BatchMigrateTemplatesRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchMigrateTemplatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchMigrateTemplatesRequest) ProtoMessage() {}

func (x *BatchMigrateTemplatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchMigrateTemplatesRequest.ProtoReflect.Descriptor instead.
func (*BatchMigrateTemplatesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{51}
}

func (x *BatchMigrateTemplatesRequest) GetTemplates() []*FulfillmentReportTemplateSync {
	if x != nil {
		return x.Templates
	}
	return nil
}

// 批量迁移模板响应
type BatchMigrateTemplatesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 成功迁移的模板数量
	SuccessCount int32 `protobuf:"varint,1,opt,name=success_count,json=successCount,proto3" json:"success_count,omitempty"`
	// 失败的模板数量
	FailedCount int32 `protobuf:"varint,2,opt,name=failed_count,json=failedCount,proto3" json:"failed_count,omitempty"`
	// 跳过的模板数量（已存在）
	SkippedCount  int32 `protobuf:"varint,3,opt,name=skipped_count,json=skippedCount,proto3" json:"skipped_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchMigrateTemplatesResponse) Reset() {
	*x = BatchMigrateTemplatesResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchMigrateTemplatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchMigrateTemplatesResponse) ProtoMessage() {}

func (x *BatchMigrateTemplatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchMigrateTemplatesResponse.ProtoReflect.Descriptor instead.
func (*BatchMigrateTemplatesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{52}
}

func (x *BatchMigrateTemplatesResponse) GetSuccessCount() int32 {
	if x != nil {
		return x.SuccessCount
	}
	return 0
}

func (x *BatchMigrateTemplatesResponse) GetFailedCount() int32 {
	if x != nil {
		return x.FailedCount
	}
	return 0
}

func (x *BatchMigrateTemplatesResponse) GetSkippedCount() int32 {
	if x != nil {
		return x.SkippedCount
	}
	return 0
}

// 批量迁移问题请求
type BatchMigrateQuestionsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 问题数据列表（复用双写接口结构）
	Questions     []*FulfillmentReportQuestionSync `protobuf:"bytes,1,rep,name=questions,proto3" json:"questions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchMigrateQuestionsRequest) Reset() {
	*x = BatchMigrateQuestionsRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchMigrateQuestionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchMigrateQuestionsRequest) ProtoMessage() {}

func (x *BatchMigrateQuestionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchMigrateQuestionsRequest.ProtoReflect.Descriptor instead.
func (*BatchMigrateQuestionsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{53}
}

func (x *BatchMigrateQuestionsRequest) GetQuestions() []*FulfillmentReportQuestionSync {
	if x != nil {
		return x.Questions
	}
	return nil
}

// 批量迁移问题响应
type BatchMigrateQuestionsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 成功迁移的问题数量
	SuccessCount int32 `protobuf:"varint,1,opt,name=success_count,json=successCount,proto3" json:"success_count,omitempty"`
	// 失败的问题数量
	FailedCount int32 `protobuf:"varint,2,opt,name=failed_count,json=failedCount,proto3" json:"failed_count,omitempty"`
	// 跳过的问题数量
	SkippedCount  int32 `protobuf:"varint,3,opt,name=skipped_count,json=skippedCount,proto3" json:"skipped_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchMigrateQuestionsResponse) Reset() {
	*x = BatchMigrateQuestionsResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchMigrateQuestionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchMigrateQuestionsResponse) ProtoMessage() {}

func (x *BatchMigrateQuestionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchMigrateQuestionsResponse.ProtoReflect.Descriptor instead.
func (*BatchMigrateQuestionsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{54}
}

func (x *BatchMigrateQuestionsResponse) GetSuccessCount() int32 {
	if x != nil {
		return x.SuccessCount
	}
	return 0
}

func (x *BatchMigrateQuestionsResponse) GetFailedCount() int32 {
	if x != nil {
		return x.FailedCount
	}
	return 0
}

func (x *BatchMigrateQuestionsResponse) GetSkippedCount() int32 {
	if x != nil {
		return x.SkippedCount
	}
	return 0
}

// 批量迁移报告请求
type BatchMigrateReportsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 报告数据列表（复用双写接口结构）
	Reports       []*FulfillmentReportSync `protobuf:"bytes,1,rep,name=reports,proto3" json:"reports,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchMigrateReportsRequest) Reset() {
	*x = BatchMigrateReportsRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchMigrateReportsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchMigrateReportsRequest) ProtoMessage() {}

func (x *BatchMigrateReportsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchMigrateReportsRequest.ProtoReflect.Descriptor instead.
func (*BatchMigrateReportsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{55}
}

func (x *BatchMigrateReportsRequest) GetReports() []*FulfillmentReportSync {
	if x != nil {
		return x.Reports
	}
	return nil
}

// 批量迁移报告响应
type BatchMigrateReportsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 成功迁移的报告数量
	SuccessCount int32 `protobuf:"varint,1,opt,name=success_count,json=successCount,proto3" json:"success_count,omitempty"`
	// 失败的报告数量
	FailedCount int32 `protobuf:"varint,2,opt,name=failed_count,json=failedCount,proto3" json:"failed_count,omitempty"`
	// 跳过的报告数量
	SkippedCount  int32 `protobuf:"varint,3,opt,name=skipped_count,json=skippedCount,proto3" json:"skipped_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchMigrateReportsResponse) Reset() {
	*x = BatchMigrateReportsResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchMigrateReportsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchMigrateReportsResponse) ProtoMessage() {}

func (x *BatchMigrateReportsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchMigrateReportsResponse.ProtoReflect.Descriptor instead.
func (*BatchMigrateReportsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{56}
}

func (x *BatchMigrateReportsResponse) GetSuccessCount() int32 {
	if x != nil {
		return x.SuccessCount
	}
	return 0
}

func (x *BatchMigrateReportsResponse) GetFailedCount() int32 {
	if x != nil {
		return x.FailedCount
	}
	return 0
}

func (x *BatchMigrateReportsResponse) GetSkippedCount() int32 {
	if x != nil {
		return x.SkippedCount
	}
	return 0
}

// 批量迁移发送记录请求
type BatchMigrateRecordsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 发送记录数据列表（复用双写接口结构）
	Records       []*FulfillmentReportSendRecordSync `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchMigrateRecordsRequest) Reset() {
	*x = BatchMigrateRecordsRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchMigrateRecordsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchMigrateRecordsRequest) ProtoMessage() {}

func (x *BatchMigrateRecordsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchMigrateRecordsRequest.ProtoReflect.Descriptor instead.
func (*BatchMigrateRecordsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{57}
}

func (x *BatchMigrateRecordsRequest) GetRecords() []*FulfillmentReportSendRecordSync {
	if x != nil {
		return x.Records
	}
	return nil
}

// 批量迁移发送记录响应
type BatchMigrateRecordsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 成功迁移的发送记录数量
	SuccessCount int32 `protobuf:"varint,1,opt,name=success_count,json=successCount,proto3" json:"success_count,omitempty"`
	// 失败的发送记录数量
	FailedCount int32 `protobuf:"varint,2,opt,name=failed_count,json=failedCount,proto3" json:"failed_count,omitempty"`
	// 跳过的发送记录数量
	SkippedCount  int32 `protobuf:"varint,3,opt,name=skipped_count,json=skippedCount,proto3" json:"skipped_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchMigrateRecordsResponse) Reset() {
	*x = BatchMigrateRecordsResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchMigrateRecordsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchMigrateRecordsResponse) ProtoMessage() {}

func (x *BatchMigrateRecordsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchMigrateRecordsResponse.ProtoReflect.Descriptor instead.
func (*BatchMigrateRecordsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{58}
}

func (x *BatchMigrateRecordsResponse) GetSuccessCount() int32 {
	if x != nil {
		return x.SuccessCount
	}
	return 0
}

func (x *BatchMigrateRecordsResponse) GetFailedCount() int32 {
	if x != nil {
		return x.FailedCount
	}
	return 0
}

func (x *BatchMigrateRecordsResponse) GetSkippedCount() int32 {
	if x != nil {
		return x.SkippedCount
	}
	return 0
}

// ------------------------------
// 数据查询接口相关消息定义
// ------------------------------
// 通过唯一键批量查询模板请求
type GetTemplatesByUniqueKeysRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 模板唯一键列表
	UniqueKeys    []*FulfillmentReportTemplateUniqueKey `protobuf:"bytes,1,rep,name=unique_keys,json=uniqueKeys,proto3" json:"unique_keys,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTemplatesByUniqueKeysRequest) Reset() {
	*x = GetTemplatesByUniqueKeysRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTemplatesByUniqueKeysRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTemplatesByUniqueKeysRequest) ProtoMessage() {}

func (x *GetTemplatesByUniqueKeysRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTemplatesByUniqueKeysRequest.ProtoReflect.Descriptor instead.
func (*GetTemplatesByUniqueKeysRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{59}
}

func (x *GetTemplatesByUniqueKeysRequest) GetUniqueKeys() []*FulfillmentReportTemplateUniqueKey {
	if x != nil {
		return x.UniqueKeys
	}
	return nil
}

// 通过唯一键批量查询模板响应
type GetTemplatesByUniqueKeysResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 模板数据列表
	Templates     []*FulfillmentReportTemplateSync `protobuf:"bytes,1,rep,name=templates,proto3" json:"templates,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTemplatesByUniqueKeysResponse) Reset() {
	*x = GetTemplatesByUniqueKeysResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTemplatesByUniqueKeysResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTemplatesByUniqueKeysResponse) ProtoMessage() {}

func (x *GetTemplatesByUniqueKeysResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTemplatesByUniqueKeysResponse.ProtoReflect.Descriptor instead.
func (*GetTemplatesByUniqueKeysResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{60}
}

func (x *GetTemplatesByUniqueKeysResponse) GetTemplates() []*FulfillmentReportTemplateSync {
	if x != nil {
		return x.Templates
	}
	return nil
}

// 通过模板唯一键批量查询问题请求
type GetQuestionsByTemplateKeysRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 模板唯一键列表
	TemplateKeys  []*FulfillmentReportTemplateUniqueKey `protobuf:"bytes,1,rep,name=template_keys,json=templateKeys,proto3" json:"template_keys,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetQuestionsByTemplateKeysRequest) Reset() {
	*x = GetQuestionsByTemplateKeysRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetQuestionsByTemplateKeysRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQuestionsByTemplateKeysRequest) ProtoMessage() {}

func (x *GetQuestionsByTemplateKeysRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQuestionsByTemplateKeysRequest.ProtoReflect.Descriptor instead.
func (*GetQuestionsByTemplateKeysRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{61}
}

func (x *GetQuestionsByTemplateKeysRequest) GetTemplateKeys() []*FulfillmentReportTemplateUniqueKey {
	if x != nil {
		return x.TemplateKeys
	}
	return nil
}

// 通过模板唯一键批量查询问题响应
type GetQuestionsByTemplateKeysResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 问题数据列表
	Questions     []*FulfillmentReportQuestionSync `protobuf:"bytes,1,rep,name=questions,proto3" json:"questions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetQuestionsByTemplateKeysResponse) Reset() {
	*x = GetQuestionsByTemplateKeysResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetQuestionsByTemplateKeysResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQuestionsByTemplateKeysResponse) ProtoMessage() {}

func (x *GetQuestionsByTemplateKeysResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQuestionsByTemplateKeysResponse.ProtoReflect.Descriptor instead.
func (*GetQuestionsByTemplateKeysResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{62}
}

func (x *GetQuestionsByTemplateKeysResponse) GetQuestions() []*FulfillmentReportQuestionSync {
	if x != nil {
		return x.Questions
	}
	return nil
}

// 通过问题唯一键批量查询问题请求（grooming迁移专用）
type GetGroomingQuestionsByQuestionKeysRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 问题唯一键列表
	QuestionKeys  []*GroomingQuestionUniqueKey `protobuf:"bytes,1,rep,name=question_keys,json=questionKeys,proto3" json:"question_keys,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGroomingQuestionsByQuestionKeysRequest) Reset() {
	*x = GetGroomingQuestionsByQuestionKeysRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[63]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGroomingQuestionsByQuestionKeysRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGroomingQuestionsByQuestionKeysRequest) ProtoMessage() {}

func (x *GetGroomingQuestionsByQuestionKeysRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[63]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGroomingQuestionsByQuestionKeysRequest.ProtoReflect.Descriptor instead.
func (*GetGroomingQuestionsByQuestionKeysRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{63}
}

func (x *GetGroomingQuestionsByQuestionKeysRequest) GetQuestionKeys() []*GroomingQuestionUniqueKey {
	if x != nil {
		return x.QuestionKeys
	}
	return nil
}

// grooming问题唯一键标识（迁移专用）
type GroomingQuestionUniqueKey struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 业务ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 问题标题
	Title         string `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GroomingQuestionUniqueKey) Reset() {
	*x = GroomingQuestionUniqueKey{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[64]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GroomingQuestionUniqueKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroomingQuestionUniqueKey) ProtoMessage() {}

func (x *GroomingQuestionUniqueKey) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[64]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroomingQuestionUniqueKey.ProtoReflect.Descriptor instead.
func (*GroomingQuestionUniqueKey) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{64}
}

func (x *GroomingQuestionUniqueKey) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GroomingQuestionUniqueKey) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GroomingQuestionUniqueKey) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

// 通过问题唯一键批量查询问题响应（grooming迁移专用）
type GetGroomingQuestionsByQuestionKeysResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 问题数据列表
	Questions     []*FulfillmentReportQuestionSync `protobuf:"bytes,1,rep,name=questions,proto3" json:"questions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGroomingQuestionsByQuestionKeysResponse) Reset() {
	*x = GetGroomingQuestionsByQuestionKeysResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[65]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGroomingQuestionsByQuestionKeysResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGroomingQuestionsByQuestionKeysResponse) ProtoMessage() {}

func (x *GetGroomingQuestionsByQuestionKeysResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[65]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGroomingQuestionsByQuestionKeysResponse.ProtoReflect.Descriptor instead.
func (*GetGroomingQuestionsByQuestionKeysResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{65}
}

func (x *GetGroomingQuestionsByQuestionKeysResponse) GetQuestions() []*FulfillmentReportQuestionSync {
	if x != nil {
		return x.Questions
	}
	return nil
}

// 通过唯一键批量查询报告请求
type GetReportsByUniqueKeysRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 报告唯一键列表
	UniqueKeys    []*FulfillmentReportUniqueKey `protobuf:"bytes,1,rep,name=unique_keys,json=uniqueKeys,proto3" json:"unique_keys,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetReportsByUniqueKeysRequest) Reset() {
	*x = GetReportsByUniqueKeysRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[66]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetReportsByUniqueKeysRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReportsByUniqueKeysRequest) ProtoMessage() {}

func (x *GetReportsByUniqueKeysRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[66]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReportsByUniqueKeysRequest.ProtoReflect.Descriptor instead.
func (*GetReportsByUniqueKeysRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{66}
}

func (x *GetReportsByUniqueKeysRequest) GetUniqueKeys() []*FulfillmentReportUniqueKey {
	if x != nil {
		return x.UniqueKeys
	}
	return nil
}

// 通过唯一键批量查询报告响应
type GetReportsByUniqueKeysResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 报告数据列表
	Reports       []*FulfillmentReportSync `protobuf:"bytes,1,rep,name=reports,proto3" json:"reports,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetReportsByUniqueKeysResponse) Reset() {
	*x = GetReportsByUniqueKeysResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[67]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetReportsByUniqueKeysResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReportsByUniqueKeysResponse) ProtoMessage() {}

func (x *GetReportsByUniqueKeysResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[67]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReportsByUniqueKeysResponse.ProtoReflect.Descriptor instead.
func (*GetReportsByUniqueKeysResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{67}
}

func (x *GetReportsByUniqueKeysResponse) GetReports() []*FulfillmentReportSync {
	if x != nil {
		return x.Reports
	}
	return nil
}

// 通过报告唯一键批量查询发送记录请求
type GetRecordsByReportKeysRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 发送记录唯一键列表（每个包含报告唯一键和发送方式）
	RecordKeys    []*SendRecordUniqueKey `protobuf:"bytes,1,rep,name=record_keys,json=recordKeys,proto3" json:"record_keys,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRecordsByReportKeysRequest) Reset() {
	*x = GetRecordsByReportKeysRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[68]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRecordsByReportKeysRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecordsByReportKeysRequest) ProtoMessage() {}

func (x *GetRecordsByReportKeysRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[68]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecordsByReportKeysRequest.ProtoReflect.Descriptor instead.
func (*GetRecordsByReportKeysRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{68}
}

func (x *GetRecordsByReportKeysRequest) GetRecordKeys() []*SendRecordUniqueKey {
	if x != nil {
		return x.RecordKeys
	}
	return nil
}

// 发送记录唯一键标识
type SendRecordUniqueKey struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 报告唯一键
	ReportUniqueKey *FulfillmentReportUniqueKey `protobuf:"bytes,1,opt,name=report_unique_key,json=reportUniqueKey,proto3" json:"report_unique_key,omitempty"`
	// 发送方式
	SendMethod    SendMethod `protobuf:"varint,2,opt,name=send_method,json=sendMethod,proto3,enum=backend.proto.fulfillment.v1.SendMethod" json:"send_method,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendRecordUniqueKey) Reset() {
	*x = SendRecordUniqueKey{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[69]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendRecordUniqueKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendRecordUniqueKey) ProtoMessage() {}

func (x *SendRecordUniqueKey) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[69]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendRecordUniqueKey.ProtoReflect.Descriptor instead.
func (*SendRecordUniqueKey) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{69}
}

func (x *SendRecordUniqueKey) GetReportUniqueKey() *FulfillmentReportUniqueKey {
	if x != nil {
		return x.ReportUniqueKey
	}
	return nil
}

func (x *SendRecordUniqueKey) GetSendMethod() SendMethod {
	if x != nil {
		return x.SendMethod
	}
	return SendMethod_SEND_METHOD_UNSPECIFIED
}

// 通过报告唯一键批量查询发送记录响应
type GetRecordsByReportKeysResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 发送记录数据列表
	Records       []*FulfillmentReportSendRecordSync `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRecordsByReportKeysResponse) Reset() {
	*x = GetRecordsByReportKeysResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[70]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRecordsByReportKeysResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecordsByReportKeysResponse) ProtoMessage() {}

func (x *GetRecordsByReportKeysResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[70]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecordsByReportKeysResponse.ProtoReflect.Descriptor instead.
func (*GetRecordsByReportKeysResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{70}
}

func (x *GetRecordsByReportKeysResponse) GetRecords() []*FulfillmentReportSendRecordSync {
	if x != nil {
		return x.Records
	}
	return nil
}

// question
type UpdateFulfillmentReportTemplateRequest_UpdateQuestion struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// question category
	Category QuestionCategory `protobuf:"varint,2,opt,name=category,proto3,enum=backend.proto.fulfillment.v1.QuestionCategory" json:"category,omitempty"`
	// question type
	Type QuestionType `protobuf:"varint,3,opt,name=type,proto3,enum=backend.proto.fulfillment.v1.QuestionType" json:"type,omitempty"`
	// system default question key
	Key string `protobuf:"bytes,4,opt,name=key,proto3" json:"key,omitempty"`
	// title
	Title string `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	// is default question
	IsDefault bool `protobuf:"varint,6,opt,name=is_default,json=isDefault,proto3" json:"is_default,omitempty"`
	// is required to fill in
	IsRequired bool `protobuf:"varint,7,opt,name=is_required,json=isRequired,proto3" json:"is_required,omitempty"`
	// is type editable
	IsTypeEditable bool `protobuf:"varint,8,opt,name=is_type_editable,json=isTypeEditable,proto3" json:"is_type_editable,omitempty"`
	// is title editable
	IsTitleEditable bool `protobuf:"varint,9,opt,name=is_title_editable,json=isTitleEditable,proto3" json:"is_title_editable,omitempty"`
	// is options editable
	IsOptionsEditable bool `protobuf:"varint,10,opt,name=is_options_editable,json=isOptionsEditable,proto3" json:"is_options_editable,omitempty"`
	// sort value, in descending order
	Sort int32 `protobuf:"varint,11,opt,name=sort,proto3" json:"sort,omitempty"`
	// extra info
	Extra         *FulfillmentReportTemplateQuestion_ExtraInfo `protobuf:"bytes,12,opt,name=extra,proto3" json:"extra,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateFulfillmentReportTemplateRequest_UpdateQuestion) Reset() {
	*x = UpdateFulfillmentReportTemplateRequest_UpdateQuestion{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[71]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateFulfillmentReportTemplateRequest_UpdateQuestion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateFulfillmentReportTemplateRequest_UpdateQuestion) ProtoMessage() {}

func (x *UpdateFulfillmentReportTemplateRequest_UpdateQuestion) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[71]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateFulfillmentReportTemplateRequest_UpdateQuestion.ProtoReflect.Descriptor instead.
func (*UpdateFulfillmentReportTemplateRequest_UpdateQuestion) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{2, 0}
}

func (x *UpdateFulfillmentReportTemplateRequest_UpdateQuestion) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateFulfillmentReportTemplateRequest_UpdateQuestion) GetCategory() QuestionCategory {
	if x != nil {
		return x.Category
	}
	return QuestionCategory_QUESTION_CATEGORY_UNSPECIFIED
}

func (x *UpdateFulfillmentReportTemplateRequest_UpdateQuestion) GetType() QuestionType {
	if x != nil {
		return x.Type
	}
	return QuestionType_QUESTION_TYPE_UNSPECIFIED
}

func (x *UpdateFulfillmentReportTemplateRequest_UpdateQuestion) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *UpdateFulfillmentReportTemplateRequest_UpdateQuestion) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UpdateFulfillmentReportTemplateRequest_UpdateQuestion) GetIsDefault() bool {
	if x != nil {
		return x.IsDefault
	}
	return false
}

func (x *UpdateFulfillmentReportTemplateRequest_UpdateQuestion) GetIsRequired() bool {
	if x != nil {
		return x.IsRequired
	}
	return false
}

func (x *UpdateFulfillmentReportTemplateRequest_UpdateQuestion) GetIsTypeEditable() bool {
	if x != nil {
		return x.IsTypeEditable
	}
	return false
}

func (x *UpdateFulfillmentReportTemplateRequest_UpdateQuestion) GetIsTitleEditable() bool {
	if x != nil {
		return x.IsTitleEditable
	}
	return false
}

func (x *UpdateFulfillmentReportTemplateRequest_UpdateQuestion) GetIsOptionsEditable() bool {
	if x != nil {
		return x.IsOptionsEditable
	}
	return false
}

func (x *UpdateFulfillmentReportTemplateRequest_UpdateQuestion) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *UpdateFulfillmentReportTemplateRequest_UpdateQuestion) GetExtra() *FulfillmentReportTemplateQuestion_ExtraInfo {
	if x != nil {
		return x.Extra
	}
	return nil
}

// FulfillmentReportRecords
type GetFulfillmentReportRecordsResponse_FulfillmentReportRecords struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// fulfillment report
	FulfillmentReport *FulfillmentReport `protobuf:"bytes,1,opt,name=fulfillment_report,json=fulfillmentReport,proto3" json:"fulfillment_report,omitempty"`
	// fulfillment report send records
	FulfillmentReportSendRecords []*FulfillmentReportSendRecord `protobuf:"bytes,2,rep,name=fulfillment_report_send_records,json=fulfillmentReportSendRecords,proto3" json:"fulfillment_report_send_records,omitempty"`
	unknownFields                protoimpl.UnknownFields
	sizeCache                    protoimpl.SizeCache
}

func (x *GetFulfillmentReportRecordsResponse_FulfillmentReportRecords) Reset() {
	*x = GetFulfillmentReportRecordsResponse_FulfillmentReportRecords{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[72]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFulfillmentReportRecordsResponse_FulfillmentReportRecords) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFulfillmentReportRecordsResponse_FulfillmentReportRecords) ProtoMessage() {}

func (x *GetFulfillmentReportRecordsResponse_FulfillmentReportRecords) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[72]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFulfillmentReportRecordsResponse_FulfillmentReportRecords.ProtoReflect.Descriptor instead.
func (*GetFulfillmentReportRecordsResponse_FulfillmentReportRecords) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{11, 0}
}

func (x *GetFulfillmentReportRecordsResponse_FulfillmentReportRecords) GetFulfillmentReport() *FulfillmentReport {
	if x != nil {
		return x.FulfillmentReport
	}
	return nil
}

func (x *GetFulfillmentReportRecordsResponse_FulfillmentReportRecords) GetFulfillmentReportSendRecords() []*FulfillmentReportSendRecord {
	if x != nil {
		return x.FulfillmentReportSendRecords
	}
	return nil
}

// fulfillment report card
type ListFulfillmentReportResponse_FulfillmentReportCard struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// report id
	ReportId int64 `protobuf:"varint,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`
	// pet
	Pet *ListFulfillmentReportResponse_FulfillmentReportCard_Pet `protobuf:"bytes,2,opt,name=pet,proto3" json:"pet,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// last update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// send time
	SendTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,6,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// send method
	SendMethod SendMethod `protobuf:"varint,7,opt,name=send_method,json=sendMethod,proto3,enum=backend.proto.fulfillment.v1.SendMethod" json:"send_method,omitempty"`
	// care type
	CareType CareType `protobuf:"varint,8,opt,name=care_type,json=careType,proto3,enum=backend.proto.fulfillment.v1.CareType" json:"care_type,omitempty"`
	// media(image/video) count
	MediaCount int32 `protobuf:"varint,9,opt,name=media_count,json=mediaCount,proto3" json:"media_count,omitempty"`
	// service date, date of report
	// (-- api-linter: core::0142::time-field-type=disabled --)
	ServiceDate string `protobuf:"bytes,10,opt,name=service_date,json=serviceDate,proto3" json:"service_date,omitempty"`
	// uuid
	Uuid          string `protobuf:"bytes,11,opt,name=uuid,proto3" json:"uuid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListFulfillmentReportResponse_FulfillmentReportCard) Reset() {
	*x = ListFulfillmentReportResponse_FulfillmentReportCard{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[73]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListFulfillmentReportResponse_FulfillmentReportCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFulfillmentReportResponse_FulfillmentReportCard) ProtoMessage() {}

func (x *ListFulfillmentReportResponse_FulfillmentReportCard) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[73]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFulfillmentReportResponse_FulfillmentReportCard.ProtoReflect.Descriptor instead.
func (*ListFulfillmentReportResponse_FulfillmentReportCard) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{25, 0}
}

func (x *ListFulfillmentReportResponse_FulfillmentReportCard) GetReportId() int64 {
	if x != nil {
		return x.ReportId
	}
	return 0
}

func (x *ListFulfillmentReportResponse_FulfillmentReportCard) GetPet() *ListFulfillmentReportResponse_FulfillmentReportCard_Pet {
	if x != nil {
		return x.Pet
	}
	return nil
}

func (x *ListFulfillmentReportResponse_FulfillmentReportCard) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *ListFulfillmentReportResponse_FulfillmentReportCard) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *ListFulfillmentReportResponse_FulfillmentReportCard) GetSendTime() *timestamppb.Timestamp {
	if x != nil {
		return x.SendTime
	}
	return nil
}

func (x *ListFulfillmentReportResponse_FulfillmentReportCard) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *ListFulfillmentReportResponse_FulfillmentReportCard) GetSendMethod() SendMethod {
	if x != nil {
		return x.SendMethod
	}
	return SendMethod_SEND_METHOD_UNSPECIFIED
}

func (x *ListFulfillmentReportResponse_FulfillmentReportCard) GetCareType() CareType {
	if x != nil {
		return x.CareType
	}
	return CareType_CARE_TYPE_UNSPECIFIED
}

func (x *ListFulfillmentReportResponse_FulfillmentReportCard) GetMediaCount() int32 {
	if x != nil {
		return x.MediaCount
	}
	return 0
}

func (x *ListFulfillmentReportResponse_FulfillmentReportCard) GetServiceDate() string {
	if x != nil {
		return x.ServiceDate
	}
	return ""
}

func (x *ListFulfillmentReportResponse_FulfillmentReportCard) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

// pet
type ListFulfillmentReportResponse_FulfillmentReportCard_Pet struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet name
	// (-- api-linter: core::0122::name-suffix=disabled --)
	PetName string `protobuf:"bytes,2,opt,name=pet_name,json=petName,proto3" json:"pet_name,omitempty"`
	// pet avatar
	AvatarPath string `protobuf:"bytes,3,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// pet type
	PetType       v1.Pet_PetType `protobuf:"varint,4,opt,name=pet_type,json=petType,proto3,enum=backend.proto.pet.v1.Pet_PetType" json:"pet_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListFulfillmentReportResponse_FulfillmentReportCard_Pet) Reset() {
	*x = ListFulfillmentReportResponse_FulfillmentReportCard_Pet{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[74]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListFulfillmentReportResponse_FulfillmentReportCard_Pet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFulfillmentReportResponse_FulfillmentReportCard_Pet) ProtoMessage() {}

func (x *ListFulfillmentReportResponse_FulfillmentReportCard_Pet) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[74]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFulfillmentReportResponse_FulfillmentReportCard_Pet.ProtoReflect.Descriptor instead.
func (*ListFulfillmentReportResponse_FulfillmentReportCard_Pet) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP(), []int{25, 0, 0}
}

func (x *ListFulfillmentReportResponse_FulfillmentReportCard_Pet) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ListFulfillmentReportResponse_FulfillmentReportCard_Pet) GetPetName() string {
	if x != nil {
		return x.PetName
	}
	return ""
}

func (x *ListFulfillmentReportResponse_FulfillmentReportCard_Pet) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *ListFulfillmentReportResponse_FulfillmentReportCard_Pet) GetPetType() v1.Pet_PetType {
	if x != nil {
		return x.PetType
	}
	return v1.Pet_PetType(0)
}

var File_backend_proto_fulfillment_v1_fulfillment_report_service_proto protoreflect.FileDescriptor

const file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDesc = "" +
	"\n" +
	"=backend/proto/fulfillment/v1/fulfillment_report_service.proto\x12\x1cbackend.proto.fulfillment.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a5backend/proto/fulfillment/v1/fulfillment_report.proto\x1a)backend/proto/fulfillment/v1/common.proto\x1a\x1ebackend/proto/pet/v1/pet.proto\x1a\x17validate/validate.proto\"\xdb\x01\n" +
	"#GetFulfillmentReportTemplateRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tcompanyId\x12-\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00H\x00R\n" +
	"businessId\x88\x01\x01\x12M\n" +
	"\tcare_type\x18\x03 \x01(\x0e2&.backend.proto.fulfillment.v1.CareTypeB\b\xfaB\x05\x82\x01\x02\x10\x01R\bcareTypeB\x0e\n" +
	"\f_business_id\"{\n" +
	"$GetFulfillmentReportTemplateResponse\x12S\n" +
	"\btemplate\x18\x01 \x01(\v27.backend.proto.fulfillment.v1.FulfillmentReportTemplateR\btemplate\"\x95\x10\n" +
	"&UpdateFulfillmentReportTemplateRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tcompanyId\x12-\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00H\x00R\n" +
	"businessId\x88\x01\x01\x12\"\n" +
	"\bstaff_id\x18\x03 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\astaffId\x12\x17\n" +
	"\x02id\x18\x04 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\x02id\x12M\n" +
	"\tcare_type\x18\x05 \x01(\x0e2&.backend.proto.fulfillment.v1.CareTypeB\b\xfaB\x05\x82\x01\x02\x10\x01R\bcareType\x12\x1e\n" +
	"\x05title\x18\x06 \x01(\tB\b\xfaB\x05r\x03\x18\xff\x01R\x05title\x12J\n" +
	"\vtheme_color\x18\a \x01(\tB)\xfaB&r$2\"^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$R\n" +
	"themeColor\x12U\n" +
	"\x11light_theme_color\x18\b \x01(\tB)\xfaB&r$2\"^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$R\x0flightThemeColor\x12'\n" +
	"\n" +
	"theme_code\x18\t \x01(\tB\b\xfaB\x05r\x03\x18\xff\x01R\tthemeCode\x124\n" +
	"\x11thank_you_message\x18\n" +
	" \x01(\tB\b\xfaB\x05r\x03\x18\xff\x01R\x0fthankYouMessage\x12#\n" +
	"\rshow_showcase\x18\v \x01(\bR\fshowShowcase\x122\n" +
	"\x15show_overall_feedback\x18\f \x01(\bR\x13showOverallFeedback\x12,\n" +
	"\x12show_pet_condition\x18\r \x01(\bR\x10showPetCondition\x12\x1d\n" +
	"\n" +
	"show_staff\x18\x0e \x01(\bR\tshowStaff\x126\n" +
	"\x17show_customize_feedback\x18\x0f \x01(\bR\x15showCustomizeFeedback\x122\n" +
	"\x15show_next_appointment\x18\x10 \x01(\bR\x13showNextAppointment\x12\x8f\x01\n" +
	"!next_appointment_date_format_type\x18\x11 \x01(\x0e2;.backend.proto.fulfillment.v1.NextAppointmentDateFormatTypeB\b\xfaB\x05\x82\x01\x02\x10\x01R\x1dnextAppointmentDateFormatType\x12.\n" +
	"\x13show_review_booster\x18\x12 \x01(\bR\x11showReviewBooster\x12(\n" +
	"\x10show_yelp_review\x18\x13 \x01(\bR\x0eshowYelpReview\x12(\n" +
	"\x10yelp_review_link\x18\x14 \x01(\tR\x0eyelpReviewLink\x12,\n" +
	"\x12show_google_review\x18\x15 \x01(\bR\x10showGoogleReview\x12,\n" +
	"\x12google_review_link\x18\x16 \x01(\tR\x10googleReviewLink\x120\n" +
	"\x14show_facebook_review\x18\x17 \x01(\bR\x12showFacebookReview\x120\n" +
	"\x14facebook_review_link\x18\x18 \x01(\tR\x12facebookReviewLink\x12q\n" +
	"\tquestions\x18\x19 \x03(\v2S.backend.proto.fulfillment.v1.UpdateFulfillmentReportTemplateRequest.UpdateQuestionR\tquestions\x128\n" +
	"\x13delete_question_ids\x18\x1a \x03(\x03B\b\xfaB\x05\x92\x01\x02\x18\x01R\x11deleteQuestionIds\x1a\xc0\x04\n" +
	"\x0eUpdateQuestion\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\x02id\x12T\n" +
	"\bcategory\x18\x02 \x01(\x0e2..backend.proto.fulfillment.v1.QuestionCategoryB\b\xfaB\x05\x82\x01\x02\x10\x01R\bcategory\x12H\n" +
	"\x04type\x18\x03 \x01(\x0e2*.backend.proto.fulfillment.v1.QuestionTypeB\b\xfaB\x05\x82\x01\x02\x10\x01R\x04type\x12\x1a\n" +
	"\x03key\x18\x04 \x01(\tB\b\xfaB\x05r\x03\x18\xff\x01R\x03key\x12\x1e\n" +
	"\x05title\x18\x05 \x01(\tB\b\xfaB\x05r\x03\x18\xff\x01R\x05title\x12\x1d\n" +
	"\n" +
	"is_default\x18\x06 \x01(\bR\tisDefault\x12\x1f\n" +
	"\vis_required\x18\a \x01(\bR\n" +
	"isRequired\x12(\n" +
	"\x10is_type_editable\x18\b \x01(\bR\x0eisTypeEditable\x12*\n" +
	"\x11is_title_editable\x18\t \x01(\bR\x0fisTitleEditable\x12.\n" +
	"\x13is_options_editable\x18\n" +
	" \x01(\bR\x11isOptionsEditable\x12\x12\n" +
	"\x04sort\x18\v \x01(\x05R\x04sort\x12_\n" +
	"\x05extra\x18\f \x01(\v2I.backend.proto.fulfillment.v1.FulfillmentReportTemplateQuestion.ExtraInfoR\x05extraB\x0e\n" +
	"\f_business_id\")\n" +
	"'UpdateFulfillmentReportTemplateResponse\"\xe9\x02\n" +
	"\x1bGetFulfillmentReportRequest\x12)\n" +
	"\treport_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00H\x00R\breportId\x88\x01\x01\x123\n" +
	"\x0eappointment_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00H\x01R\rappointmentId\x88\x01\x01\x12#\n" +
	"\x06pet_id\x18\x03 \x01(\x03B\a\xfaB\x04\"\x02 \x00H\x02R\x05petId\x88\x01\x01\x12R\n" +
	"\tcare_type\x18\x04 \x01(\x0e2&.backend.proto.fulfillment.v1.CareTypeB\b\xfaB\x05\x82\x01\x02\x10\x01H\x03R\bcareType\x88\x01\x01\x12&\n" +
	"\fservice_date\x18\x05 \x01(\tH\x04R\vserviceDate\x88\x01\x01B\f\n" +
	"\n" +
	"_report_idB\x11\n" +
	"\x0f_appointment_idB\t\n" +
	"\a_pet_idB\f\n" +
	"\n" +
	"_care_typeB\x0f\n" +
	"\r_service_date\"\xa6\x01\n" +
	"\x1cGetFulfillmentReportResponse\x12^\n" +
	"\x12fulfillment_report\x18\x01 \x01(\v2/.backend.proto.fulfillment.v1.FulfillmentReportR\x11fulfillmentReport\x12&\n" +
	"\x0fis_need_refresh\x18\x02 \x01(\bR\risNeedRefresh\"\xe1\x04\n" +
	"\x1eUpdateFulfillmentReportRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tcompanyId\x12-\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00H\x00R\n" +
	"businessId\x88\x01\x01\x12\"\n" +
	"\bstaff_id\x18\x03 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\astaffId\x12\x1c\n" +
	"\x02id\x18\x04 \x01(\x03B\a\xfaB\x04\"\x02 \x00H\x01R\x02id\x88\x01\x01\x12(\n" +
	"\vcustomer_id\x18\x05 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\n" +
	"customerId\x12.\n" +
	"\x0eappointment_id\x18\x06 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\rappointmentId\x12\x1e\n" +
	"\x06pet_id\x18\a \x01(\x03B\a\xfaB\x04\"\x02 \x00R\x05petId\x12M\n" +
	"\tcare_type\x18\b \x01(\x0e2&.backend.proto.fulfillment.v1.CareTypeB\b\xfaB\x05\x82\x01\x02\x10\x01R\bcareType\x12&\n" +
	"\fservice_date\x18\n" +
	" \x01(\tH\x02R\vserviceDate\x88\x01\x01\x12Z\n" +
	"\acontent\x18\v \x01(\v26.backend.proto.fulfillment.v1.FulfillmentReportContentB\b\xfaB\x05\x8a\x01\x02\x10\x01R\acontent\x12\"\n" +
	"\n" +
	"theme_code\x18\f \x01(\tH\x03R\tthemeCode\x88\x01\x01B\x0e\n" +
	"\f_business_idB\x05\n" +
	"\x03_idB\x0f\n" +
	"\r_service_dateB\r\n" +
	"\v_theme_code\"\xa9\x01\n" +
	"\x1fUpdateFulfillmentReportResponse\x12^\n" +
	"\x12fulfillment_report\x18\x01 \x01(\v2/.backend.proto.fulfillment.v1.FulfillmentReportR\x11fulfillmentReport\x12&\n" +
	"\x0fis_need_refresh\x18\x02 \x01(\bR\risNeedRefresh\"\xa6\x01\n" +
	"&GetFulfillmentReportSummaryInfoRequest\x12@\n" +
	"\x15fulfillment_report_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00H\x00R\x13fulfillmentReportId\x88\x01\x01\x12\x17\n" +
	"\x04uuid\x18\x02 \x01(\tH\x01R\x04uuid\x88\x01\x01B\x18\n" +
	"\x16_fulfillment_report_idB\a\n" +
	"\x05_uuid\"\x8c\x01\n" +
	"'GetFulfillmentReportSummaryInfoResponse\x12a\n" +
	"\fsummary_info\x18\x01 \x01(\v2>.backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfoR\vsummaryInfo\"\xf4\x01\n" +
	"\"GetFulfillmentReportRecordsRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tcompanyId\x12-\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00H\x00R\n" +
	"businessId\x88\x01\x01\x12.\n" +
	"\x0eappointment_id\x18\x03 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\rappointmentId\x12&\n" +
	"\fservice_date\x18\x04 \x01(\tH\x01R\vserviceDate\x88\x01\x01B\x0e\n" +
	"\f_business_idB\x0f\n" +
	"\r_service_date\"\xc0\x03\n" +
	"#GetFulfillmentReportRecordsResponse\x12\x98\x01\n" +
	"\x1afulfillment_report_records\x18\x01 \x03(\v2Z.backend.proto.fulfillment.v1.GetFulfillmentReportRecordsResponse.FulfillmentReportRecordsR\x18fulfillmentReportRecords\x1a\xfd\x01\n" +
	"\x18FulfillmentReportRecords\x12^\n" +
	"\x12fulfillment_report\x18\x01 \x01(\v2/.backend.proto.fulfillment.v1.FulfillmentReportR\x11fulfillmentReport\x12\x80\x01\n" +
	"\x1ffulfillment_report_send_records\x18\x02 \x03(\v29.backend.proto.fulfillment.v1.FulfillmentReportSendRecordR\x1cfulfillmentReportSendRecords\"\xc6\x02\n" +
	"\"GetFulfillmentReportPreviewRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tcompanyId\x12-\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00H\x00R\n" +
	"businessId\x88\x01\x01\x12M\n" +
	"\tcare_type\x18\x03 \x01(\x0e2&.backend.proto.fulfillment.v1.CareTypeB\b\xfaB\x05\x82\x01\x02\x10\x01R\bcareType\x12)\n" +
	"\treport_id\x18\x04 \x01(\x03B\a\xfaB\x04\"\x02 \x00H\x01R\breportId\x88\x01\x01\x12\"\n" +
	"\n" +
	"theme_code\x18\x05 \x01(\tH\x02R\tthemeCode\x88\x01\x01B\x0e\n" +
	"\f_business_idB\f\n" +
	"\n" +
	"_report_idB\r\n" +
	"\v_theme_code\"\xe7\x01\n" +
	"#GetFulfillmentReportPreviewResponse\x12a\n" +
	"\fsummary_info\x18\x01 \x01(\v2>.backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfoR\vsummaryInfo\x12]\n" +
	"\fsample_value\x18\x02 \x01(\v2:.backend.proto.fulfillment.v1.FulfillmentReportSampleValueR\vsampleValue\"K\n" +
	"!ListFulfillmentThemeConfigRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tcompanyId\"\x85\x01\n" +
	"\"ListFulfillmentThemeConfigResponse\x12_\n" +
	"\rtheme_configs\x18\x01 \x03(\v2:.backend.proto.fulfillment.v1.FulfillmentReportThemeConfigR\fthemeConfigs\"\x9f\x02\n" +
	"\x1dGenerateMessageContentRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tcompanyId\x12.\n" +
	"\x0eappointment_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\rappointmentId\x12\x1e\n" +
	"\x06pet_id\x18\x03 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\x05petId\x12&\n" +
	"\fservice_date\x18\x04 \x01(\tH\x00R\vserviceDate\x88\x01\x01\x12M\n" +
	"\tcare_type\x18\x05 \x01(\x0e2&.backend.proto.fulfillment.v1.CareTypeB\b\xfaB\x05\x82\x01\x02\x10\x01R\bcareTypeB\x0f\n" +
	"\r_service_date\"I\n" +
	"\x1eGenerateMessageContentResponse\x12'\n" +
	"\x0fmessage_content\x18\x01 \x01(\tR\x0emessageContent\"\xb3\x02\n" +
	"\x1cSendFulfillmentReportRequest\x12;\n" +
	"\x15fulfillment_report_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\x13fulfillmentReportId\x12S\n" +
	"\vsend_method\x18\x02 \x01(\x0e2(.backend.proto.fulfillment.v1.SendMethodB\b\xfaB\x05\x82\x01\x02\x10\x01R\n" +
	"sendMethod\x12;\n" +
	"\x10recipient_emails\x18\x03 \x03(\tB\x10\xfaB\r\x92\x01\n" +
	"\x18\x01\"\x06r\x04\x10\x01`\x01R\x0frecipientEmails\x122\n" +
	"\remail_subject\x18\x04 \x01(\tB\b\xfaB\x05r\x03\x18\xff\x01H\x00R\femailSubject\x88\x01\x01B\x10\n" +
	"\x0e_email_subject\"{\n" +
	"\x1dSendFulfillmentReportResponse\x12Z\n" +
	"\vsend_result\x18\x01 \x01(\v29.backend.proto.fulfillment.v1.FulfillmentReportSendResultR\n" +
	"sendResult\"\xef\x01\n" +
	"&GetFulfillmentReportSendHistoryRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tcompanyId\x12.\n" +
	"\x0eappointment_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\rappointmentId\x12\x1e\n" +
	"\x06pet_id\x18\x03 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\x05petId\x12M\n" +
	"\tcare_type\x18\x04 \x01(\x0e2&.backend.proto.fulfillment.v1.CareTypeB\b\xfaB\x05\x82\x01\x02\x10\x01R\bcareType\"\x87\x01\n" +
	"'GetFulfillmentReportSendHistoryResponse\x12\\\n" +
	"\fsend_records\x18\x01 \x03(\v29.backend.proto.fulfillment.v1.FulfillmentReportSendRecordR\vsendRecords\"\x96\x03\n" +
	"%GetFulfillmentReportSendResultRequest\x12@\n" +
	"\x15fulfillment_report_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00H\x00R\x13fulfillmentReportId\x88\x01\x01\x123\n" +
	"\x0eappointment_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00H\x01R\rappointmentId\x88\x01\x01\x12#\n" +
	"\x06pet_id\x18\x03 \x01(\x03B\a\xfaB\x04\"\x02 \x00H\x02R\x05petId\x88\x01\x01\x12R\n" +
	"\tcare_type\x18\x04 \x01(\x0e2&.backend.proto.fulfillment.v1.CareTypeB\b\xfaB\x05\x82\x01\x02\x10\x01H\x03R\bcareType\x88\x01\x01\x12&\n" +
	"\fservice_date\x18\x05 \x01(\tH\x04R\vserviceDate\x88\x01\x01B\x18\n" +
	"\x16_fulfillment_report_idB\x11\n" +
	"\x0f_appointment_idB\t\n" +
	"\a_pet_idB\f\n" +
	"\n" +
	"_care_typeB\x0f\n" +
	"\r_service_date\"\x86\x01\n" +
	"&GetFulfillmentReportSendResultResponse\x12\\\n" +
	"\fsend_results\x18\x01 \x03(\v29.backend.proto.fulfillment.v1.FulfillmentReportSendResultR\vsendResults\"\xa0\x02\n" +
	"\x1cListFulfillmentReportRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tcompanyId\x12(\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\n" +
	"businessId\x12W\n" +
	"\x06filter\x18\x03 \x01(\v2?.backend.proto.fulfillment.v1.ListFulfillmentReportConfigFilterR\x06filter\x12U\n" +
	"\n" +
	"pagination\x18\x04 \x01(\v2+.backend.proto.fulfillment.v1.PaginationRefB\b\xfaB\x05\x8a\x01\x02\x10\x01R\n" +
	"pagination\"\xef\a\n" +
	"\x1dListFulfillmentReportResponse\x12\x8b\x01\n" +
	"\x18fulfillment_report_cards\x18\x01 \x03(\v2Q.backend.proto.fulfillment.v1.ListFulfillmentReportResponse.FulfillmentReportCardR\x16fulfillmentReportCards\x12K\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2+.backend.proto.fulfillment.v1.PaginationRefR\n" +
	"pagination\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05total\x1a\xdc\x05\n" +
	"\x15FulfillmentReportCard\x12\x1b\n" +
	"\treport_id\x18\x01 \x01(\x03R\breportId\x12g\n" +
	"\x03pet\x18\x02 \x01(\v2U.backend.proto.fulfillment.v1.ListFulfillmentReportResponse.FulfillmentReportCard.PetR\x03pet\x12\x1f\n" +
	"\vcustomer_id\x18\x03 \x01(\x03R\n" +
	"customerId\x12;\n" +
	"\vupdate_time\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x127\n" +
	"\tsend_time\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\bsendTime\x12%\n" +
	"\x0eappointment_id\x18\x06 \x01(\x03R\rappointmentId\x12I\n" +
	"\vsend_method\x18\a \x01(\x0e2(.backend.proto.fulfillment.v1.SendMethodR\n" +
	"sendMethod\x12C\n" +
	"\tcare_type\x18\b \x01(\x0e2&.backend.proto.fulfillment.v1.CareTypeR\bcareType\x12\x1f\n" +
	"\vmedia_count\x18\t \x01(\x05R\n" +
	"mediaCount\x12!\n" +
	"\fservice_date\x18\n" +
	" \x01(\tR\vserviceDate\x12\x12\n" +
	"\x04uuid\x18\v \x01(\tR\x04uuid\x1a\x96\x01\n" +
	"\x03Pet\x12\x15\n" +
	"\x06pet_id\x18\x01 \x01(\x03R\x05petId\x12\x19\n" +
	"\bpet_name\x18\x02 \x01(\tR\apetName\x12\x1f\n" +
	"\vavatar_path\x18\x03 \x01(\tR\n" +
	"avatarPath\x12<\n" +
	"\bpet_type\x18\x04 \x01(\x0e2!.backend.proto.pet.v1.Pet.PetTypeR\apetType\"\xed\x02\n" +
	"!ListFulfillmentReportConfigFilter\x12S\n" +
	"\x06status\x18\x01 \x01(\x0e2*.backend.proto.fulfillment.v1.ReportStatusB\n" +
	"\xfaB\a\x82\x01\x04\x10\x01 \x00H\x00R\x06status\x88\x01\x01\x12X\n" +
	"\n" +
	"care_types\x18\x02 \x03(\x0e2&.backend.proto.fulfillment.v1.CareTypeB\x11\xfaB\x0e\x92\x01\v\x18\x01\"\a\x82\x01\x04\x10\x01 \x00R\tcareTypes\x12\"\n" +
	"\n" +
	"start_date\x18\x03 \x01(\tH\x01R\tstartDate\x88\x01\x01\x12\x1e\n" +
	"\bend_date\x18\x04 \x01(\tH\x02R\aendDate\x88\x01\x01\x12#\n" +
	"\x06pet_id\x18\x05 \x01(\x03B\a\xfaB\x04\"\x02 \x00H\x03R\x05petId\x88\x01\x01B\t\n" +
	"\a_statusB\r\n" +
	"\v_start_dateB\v\n" +
	"\t_end_dateB\t\n" +
	"\a_pet_id\"\xaf\x01\n" +
	"#BatchDeleteFulfillmentReportRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tcompanyId\x12(\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\n" +
	"businessId\x126\n" +
	"\x0freport_card_ids\x18\x03 \x03(\x03B\x0e\xfaB\v\x92\x01\b\x18\x01\"\x04\"\x02 \x00R\rreportCardIds\"&\n" +
	"$BatchDeleteFulfillmentReportResponse\"\x9f\x02\n" +
	"!BatchSendFulfillmentReportRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tcompanyId\x12(\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\n" +
	"businessId\x12-\n" +
	"\n" +
	"report_ids\x18\x03 \x03(\x03B\x0e\xfaB\v\x92\x01\b\x18\x01\"\x04\"\x02 \x00R\treportIds\x12U\n" +
	"\vsend_method\x18\x04 \x01(\x0e2(.backend.proto.fulfillment.v1.SendMethodB\n" +
	"\xfaB\a\x82\x01\x04\x10\x01 \x00R\n" +
	"sendMethod\x12\"\n" +
	"\bstaff_id\x18\x05 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\astaffId\"\x82\x01\n" +
	"\"BatchSendFulfillmentReportResponse\x12\\\n" +
	"\fsend_results\x18\x01 \x03(\v29.backend.proto.fulfillment.v1.FulfillmentReportSendResultR\vsendResults\"D\n" +
	"%IncreaseFulfillmentOpenedCountRequest\x12\x1b\n" +
	"\x04uuid\x18\x01 \x01(\tB\a\xfaB\x04r\x02\x182R\x04uuid\"(\n" +
	"&IncreaseFulfillmentOpenedCountResponse\"\xca\x01\n" +
	"\x1dCountFulfillmentReportRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tcompanyId\x12(\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\n" +
	"businessId\x12W\n" +
	"\x06filter\x18\x03 \x01(\v2?.backend.proto.fulfillment.v1.ListFulfillmentReportConfigFilterR\x06filter\"v\n" +
	"\x1eCountFulfillmentReportResponse\x12\x14\n" +
	"\x05total\x18\x01 \x01(\x05R\x05total\x12\x1f\n" +
	"\vdraft_count\x18\x02 \x01(\x05R\n" +
	"draftCount\x12\x1d\n" +
	"\n" +
	"sent_count\x18\x03 \x01(\x05R\tsentCount\"\xcb\t\n" +
	"\x1dFulfillmentReportTemplateSync\x12\x1c\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00H\x00R\x02id\x88\x01\x01\x12&\n" +
	"\n" +
	"company_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tcompanyId\x12(\n" +
	"\vbusiness_id\x18\x03 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\n" +
	"businessId\x12M\n" +
	"\tcare_type\x18\x04 \x01(\x0e2&.backend.proto.fulfillment.v1.CareTypeB\b\xfaB\x05\x82\x01\x02\x10\x01R\bcareType\x124\n" +
	"\x11thank_you_message\x18\x05 \x01(\tB\b\xfaB\x05r\x03\x18\xff\x01R\x0fthankYouMessage\x12(\n" +
	"\vtheme_color\x18\x06 \x01(\tB\a\xfaB\x04r\x02\x18\x1eR\n" +
	"themeColor\x123\n" +
	"\x11light_theme_color\x18\a \x01(\tB\a\xfaB\x04r\x02\x18\x1eR\x0flightThemeColor\x12#\n" +
	"\rshow_showcase\x18\b \x01(\bR\fshowShowcase\x122\n" +
	"\x15show_overall_feedback\x18\t \x01(\bR\x13showOverallFeedback\x12,\n" +
	"\x12show_pet_condition\x18\n" +
	" \x01(\bR\x10showPetCondition\x125\n" +
	"\x17show_service_staff_name\x18\v \x01(\bR\x14showServiceStaffName\x122\n" +
	"\x15show_next_appointment\x18\f \x01(\bR\x13showNextAppointment\x12\x85\x01\n" +
	"!next_appointment_date_format_type\x18\r \x01(\x0e2;.backend.proto.fulfillment.v1.NextAppointmentDateFormatTypeR\x1dnextAppointmentDateFormatType\x12.\n" +
	"\x13show_review_booster\x18\x0e \x01(\bR\x11showReviewBooster\x12(\n" +
	"\x10show_yelp_review\x18\x0f \x01(\bR\x0eshowYelpReview\x12,\n" +
	"\x12show_google_review\x18\x10 \x01(\bR\x10showGoogleReview\x120\n" +
	"\x14show_facebook_review\x18\x11 \x01(\bR\x12showFacebookReview\x12F\n" +
	"\x11last_publish_time\x18\x1c \x01(\v2\x1a.google.protobuf.TimestampR\x0flastPublishTime\x12\x1d\n" +
	"\x05title\x18\x13 \x01(\tB\a\xfaB\x04r\x02\x182R\x05title\x12\x1b\n" +
	"\tupdate_by\x18\x14 \x01(\x03R\bupdateBy\x12;\n" +
	"\vcreate_time\x18\x15 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x16 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12\x1d\n" +
	"\n" +
	"theme_code\x18\x17 \x01(\tR\tthemeCodeB\x05\n" +
	"\x03_id\"\xc5\x01\n" +
	"\"FulfillmentReportTemplateUniqueKey\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tcompanyId\x12(\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\n" +
	"businessId\x12M\n" +
	"\tcare_type\x18\x03 \x01(\x0e2&.backend.proto.fulfillment.v1.CareTypeB\b\xfaB\x05\x82\x01\x02\x10\x01R\bcareType\"\x83\x03\n" +
	"$SyncFulfillmentReportTemplateRequest\x12S\n" +
	"\toperation\x18\x01 \x01(\x0e2+.backend.proto.fulfillment.v1.SyncOperationB\b\xfaB\x05\x82\x01\x02\x10\x01R\toperation\x12*\n" +
	"\vtemplate_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00H\x00R\n" +
	"templateId\x12a\n" +
	"\n" +
	"unique_key\x18\x03 \x01(\<EMAIL>\x00R\tuniqueKey\x12\\\n" +
	"\btemplate\x18\x04 \x01(\v2;.backend.proto.fulfillment.v1.FulfillmentReportTemplateSyncH\x01R\btemplate\x88\x01\x01B\f\n" +
	"\n" +
	"identifierB\v\n" +
	"\t_template\"\xaf\x01\n" +
	"%SyncFulfillmentReportTemplateResponse\x12\x1f\n" +
	"\vtemplate_id\x18\x01 \x01(\x03R\n" +
	"templateId\x12@\n" +
	"\x06status\x18\x02 \x01(\x0e2(.backend.proto.fulfillment.v1.SyncStatusR\x06status\x12#\n" +
	"\rerror_message\x18\x03 \x01(\tR\ferrorMessage\"\xb2\x06\n" +
	"\x1dFulfillmentReportQuestionSync\x12\x1c\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00H\x00R\x02id\x88\x01\x01\x12&\n" +
	"\n" +
	"company_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tcompanyId\x12(\n" +
	"\vbusiness_id\x18\x03 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\n" +
	"businessId\x12M\n" +
	"\tcare_type\x18\x04 \x01(\x0e2&.backend.proto.fulfillment.v1.CareTypeB\b\xfaB\x05\x82\x01\x02\x10\x01R\bcareType\x12T\n" +
	"\bcategory\x18\x05 \x01(\x0e2..backend.proto.fulfillment.v1.QuestionCategoryB\b\xfaB\x05\x82\x01\x02\x10\x01R\bcategory\x12H\n" +
	"\x04type\x18\x06 \x01(\x0e2*.backend.proto.fulfillment.v1.QuestionTypeB\b\xfaB\x05\x82\x01\x02\x10\x01R\x04type\x12\x19\n" +
	"\x03key\x18\a \x01(\tB\a\xfaB\x04r\x02\x182R\x03key\x12\x1d\n" +
	"\x05title\x18\b \x01(\tB\a\xfaB\x04r\x02\x182R\x05title\x12\x1d\n" +
	"\n" +
	"extra_json\x18\t \x01(\tR\textraJson\x12\x1d\n" +
	"\n" +
	"is_default\x18\n" +
	" \x01(\bR\tisDefault\x12\x1f\n" +
	"\vis_required\x18\v \x01(\bR\n" +
	"isRequired\x12(\n" +
	"\x10is_type_editable\x18\f \x01(\bR\x0eisTypeEditable\x12*\n" +
	"\x11is_title_editable\x18\r \x01(\bR\x0fisTitleEditable\x12.\n" +
	"\x13is_options_editable\x18\x0e \x01(\bR\x11isOptionsEditable\x12\x12\n" +
	"\x04sort\x18\x0f \x01(\x05R\x04sort\x12;\n" +
	"\vcreate_time\x18\x10 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x11 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTimeB\x05\n" +
	"\x03_id\"\xca\x01\n" +
	"\x1aQuestionTemplateIdentifier\x12*\n" +
	"\vtemplate_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00H\x00R\n" +
	"templateId\x12r\n" +
	"\x13template_unique_key\x18\x02 \x01(\<EMAIL>\x00R\x11templateUniqueKeyB\f\n" +
	"\n" +
	"identifier\"\xd1\x02\n" +
	"*BatchSyncFulfillmentReportQuestionsRequest\x12S\n" +
	"\toperation\x18\x01 \x01(\x0e2+.backend.proto.fulfillment.v1.SyncOperationB\b\xfaB\x05\x82\x01\x02\x10\x01R\toperation\x12s\n" +
	"\x13template_identifier\x18\x02 \x01(\v28.backend.proto.fulfillment.v1.QuestionTemplateIdentifierB\b\xfaB\x05\x8a\x01\x02\x10\x01R\x12templateIdentifier\x12Y\n" +
	"\tquestions\x18\x03 \x03(\v2;.backend.proto.fulfillment.v1.FulfillmentReportQuestionSyncR\tquestions\"\xb5\x01\n" +
	"+BatchSyncFulfillmentReportQuestionsResponse\x12@\n" +
	"\x06status\x18\x02 \x01(\x0e2(.backend.proto.fulfillment.v1.SyncStatusR\x06status\x12#\n" +
	"\rerror_message\x18\x03 \x01(\tR\ferrorMessage\x12\x1f\n" +
	"\vtemplate_id\x18\x04 \x01(\x03R\n" +
	"templateId\"\xd2\x06\n" +
	"\x15FulfillmentReportSync\x12\x1c\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00H\x00R\x02id\x88\x01\x01\x12&\n" +
	"\n" +
	"company_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tcompanyId\x12(\n" +
	"\vbusiness_id\x18\x03 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\n" +
	"businessId\x12(\n" +
	"\vcustomer_id\x18\x04 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\n" +
	"customerId\x12.\n" +
	"\x0eappointment_id\x18\x05 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\rappointmentId\x12M\n" +
	"\tcare_type\x18\x06 \x01(\x0e2&.backend.proto.fulfillment.v1.CareTypeB\b\xfaB\x05\x82\x01\x02\x10\x01R\bcareType\x12\x1e\n" +
	"\x06pet_id\x18\a \x01(\x03B\a\xfaB\x04\"\x02 \x00R\x05petId\x12'\n" +
	"\vpet_type_id\x18\b \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tpetTypeId\x12\x1b\n" +
	"\x04uuid\x18\t \x01(\tB\a\xfaB\x04r\x02\x182R\x04uuid\x12E\n" +
	"\x10template_version\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\x0ftemplateVersion\x12#\n" +
	"\rtemplate_json\x18\v \x01(\tR\ftemplateJson\x12!\n" +
	"\fcontent_json\x18\f \x01(\tR\vcontentJson\x12\x1f\n" +
	"\x06status\x18\r \x01(\tB\a\xfaB\x04r\x02\x18\x14R\x06status\x12*\n" +
	"\x11link_opened_count\x18\x0e \x01(\x05R\x0flinkOpenedCount\x12!\n" +
	"\fservice_date\x18\x0f \x01(\tR\vserviceDate\x12\x1b\n" +
	"\tupdate_by\x18\x10 \x01(\x03R\bupdateBy\x12;\n" +
	"\vcreate_time\x18\x11 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x12 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12\x1d\n" +
	"\n" +
	"theme_code\x18\x13 \x01(\tR\tthemeCodeB\x05\n" +
	"\x03_id\"\x88\x02\n" +
	"\x1aFulfillmentReportUniqueKey\x12(\n" +
	"\vbusiness_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\n" +
	"businessId\x12.\n" +
	"\x0eappointment_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\rappointmentId\x12\x1e\n" +
	"\x06pet_id\x18\x03 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\x05petId\x12M\n" +
	"\tcare_type\x18\x04 \x01(\x0e2&.backend.proto.fulfillment.v1.CareTypeB\b\xfaB\x05\x82\x01\x02\x10\x01R\bcareType\x12!\n" +
	"\fservice_date\x18\x05 \x01(\tR\vserviceDate\"\xe1\x02\n" +
	"\x1cSyncFulfillmentReportRequest\x12S\n" +
	"\toperation\x18\x01 \x01(\x0e2+.backend.proto.fulfillment.v1.SyncOperationB\b\xfaB\x05\x82\x01\x02\x10\x01R\toperation\x12&\n" +
	"\treport_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00H\x00R\breportId\x12Y\n" +
	"\n" +
	"unique_key\x18\x03 \x01(\v28.backend.proto.fulfillment.v1.FulfillmentReportUniqueKeyH\x00R\tuniqueKey\x12P\n" +
	"\x06report\x18\x04 \x01(\v23.backend.proto.fulfillment.v1.FulfillmentReportSyncH\x01R\x06report\x88\x01\x01B\f\n" +
	"\n" +
	"identifierB\t\n" +
	"\a_report\"\xa3\x01\n" +
	"\x1dSyncFulfillmentReportResponse\x12\x1b\n" +
	"\treport_id\x18\x01 \x01(\x03R\breportId\x12@\n" +
	"\x06status\x18\x02 \x01(\x0e2(.backend.proto.fulfillment.v1.SyncStatusR\x06status\x12#\n" +
	"\rerror_message\x18\x03 \x01(\tR\ferrorMessage\"\xa9\x05\n" +
	"\x1fFulfillmentReportSendRecordSync\x12\x1c\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00H\x00R\x02id\x88\x01\x01\x12$\n" +
	"\treport_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\breportId\x12&\n" +
	"\n" +
	"company_id\x18\x03 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tcompanyId\x12(\n" +
	"\vbusiness_id\x18\x04 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\n" +
	"businessId\x12.\n" +
	"\x0eappointment_id\x18\x05 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\rappointmentId\x12!\n" +
	"\fcontent_json\x18\x06 \x01(\tR\vcontentJson\x12\x1e\n" +
	"\x06pet_id\x18\a \x01(\x03B\a\xfaB\x04\"\x02 \x00R\x05petId\x12S\n" +
	"\vsend_method\x18\b \x01(\x0e2(.backend.proto.fulfillment.v1.SendMethodB\b\xfaB\x05\x82\x01\x02\x10\x01R\n" +
	"sendMethod\x127\n" +
	"\tsent_time\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\bsentTime\x12\x17\n" +
	"\asent_by\x18\n" +
	" \x01(\x03R\x06sentBy\x12-\n" +
	"\rerror_message\x18\v \x01(\tB\b\xfaB\x05r\x03\x18\xf4\x03R\ferrorMessage\x12&\n" +
	"\x0fis_sent_success\x18\f \x01(\bR\risSentSuccess\x12;\n" +
	"\vcreate_time\x18\r \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x0e \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTimeB\x05\n" +
	"\x03_id\"\xdb\x01\n" +
	"\x14SendRecordIdentifier\x12S\n" +
	"\vsend_method\x18\x01 \x01(\x0e2(.backend.proto.fulfillment.v1.SendMethodB\b\xfaB\x05\x82\x01\x02\x10\x01R\n" +
	"sendMethod\x12n\n" +
	"\x11report_unique_key\x18\x02 \x01(\v28.backend.proto.fulfillment.v1.FulfillmentReportUniqueKeyB\b\xfaB\x05\x8a\x01\x02\x10\x01R\x0freportUniqueKey\"\x8f\x03\n" +
	"&SyncFulfillmentReportSendRecordRequest\x12S\n" +
	"\toperation\x18\x01 \x01(\x0e2+.backend.proto.fulfillment.v1.SyncOperationB\b\xfaB\x05\x82\x01\x02\x10\x01R\toperation\x12&\n" +
	"\trecord_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00H\x00R\brecordId\x12e\n" +
	"\x13business_identifier\x18\x03 \x01(\v22.backend.proto.fulfillment.v1.SendRecordIdentifierH\x00R\x12businessIdentifier\x12c\n" +
	"\vsend_record\x18\x04 \x01(\v2=.backend.proto.fulfillment.v1.FulfillmentReportSendRecordSyncH\x01R\n" +
	"sendRecord\x88\x01\x01B\f\n" +
	"\n" +
	"identifierB\x0e\n" +
	"\f_send_record\"\xca\x01\n" +
	"'SyncFulfillmentReportSendRecordResponse\x12\x1b\n" +
	"\trecord_id\x18\x01 \x01(\x03R\brecordId\x12\x1b\n" +
	"\treport_id\x18\x02 \x01(\x03R\breportId\x12@\n" +
	"\x06status\x18\x03 \x01(\x0e2(.backend.proto.fulfillment.v1.SyncStatusR\x06status\x12#\n" +
	"\rerror_message\x18\x04 \x01(\tR\ferrorMessage\"y\n" +
	"\x1cBatchMigrateTemplatesRequest\x12Y\n" +
	"\ttemplates\x18\x01 \x03(\v2;.backend.proto.fulfillment.v1.FulfillmentReportTemplateSyncR\ttemplates\"\x8c\x01\n" +
	"\x1dBatchMigrateTemplatesResponse\x12#\n" +
	"\rsuccess_count\x18\x01 \x01(\x05R\fsuccessCount\x12!\n" +
	"\ffailed_count\x18\x02 \x01(\x05R\vfailedCount\x12#\n" +
	"\rskipped_count\x18\x03 \x01(\x05R\fskippedCount\"\x86\x01\n" +
	"\x1cBatchMigrateQuestionsRequest\x12f\n" +
	"\tquestions\x18\x01 \x03(\v2;.backend.proto.fulfillment.v1.FulfillmentReportQuestionSyncB\v\xfaB\b\x92\x01\x05\b\x01\x10\xd0\x0fR\tquestions\"\x8c\x01\n" +
	"\x1dBatchMigrateQuestionsResponse\x12#\n" +
	"\rsuccess_count\x18\x01 \x01(\x05R\fsuccessCount\x12!\n" +
	"\ffailed_count\x18\x02 \x01(\x05R\vfailedCount\x12#\n" +
	"\rskipped_count\x18\x03 \x01(\x05R\fskippedCount\"k\n" +
	"\x1aBatchMigrateReportsRequest\x12M\n" +
	"\areports\x18\x01 \x03(\v23.backend.proto.fulfillment.v1.FulfillmentReportSyncR\areports\"\x8a\x01\n" +
	"\x1bBatchMigrateReportsResponse\x12#\n" +
	"\rsuccess_count\x18\x01 \x01(\x05R\fsuccessCount\x12!\n" +
	"\ffailed_count\x18\x02 \x01(\x05R\vfailedCount\x12#\n" +
	"\rskipped_count\x18\x03 \x01(\x05R\fskippedCount\"u\n" +
	"\x1aBatchMigrateRecordsRequest\x12W\n" +
	"\arecords\x18\x01 \x03(\v2=.backend.proto.fulfillment.v1.FulfillmentReportSendRecordSyncR\arecords\"\x8a\x01\n" +
	"\x1bBatchMigrateRecordsResponse\x12#\n" +
	"\rsuccess_count\x18\x01 \x01(\x05R\fsuccessCount\x12!\n" +
	"\ffailed_count\x18\x02 \x01(\x05R\vfailedCount\x12#\n" +
	"\rskipped_count\x18\x03 \x01(\x05R\fskippedCount\"\x91\x01\n" +
	"\x1fGetTemplatesByUniqueKeysRequest\x12n\n" +
	"\vunique_keys\x18\x01 \x03(\<EMAIL>\v\xfaB\b\x92\x01\x05\b\x01\x10\xd0\x0fR\n" +
	"uniqueKeys\"}\n" +
	" GetTemplatesByUniqueKeysResponse\x12Y\n" +
	"\ttemplates\x18\x01 \x03(\v2;.backend.proto.fulfillment.v1.FulfillmentReportTemplateSyncR\ttemplates\"\x97\x01\n" +
	"!GetQuestionsByTemplateKeysRequest\x12r\n" +
	"\rtemplate_keys\x18\x01 \x03(\<EMAIL>\v\xfaB\b\x92\x01\x05\b\x01\x10\xd0\x0fR\ftemplateKeys\"\x7f\n" +
	"\"GetQuestionsByTemplateKeysResponse\x12Y\n" +
	"\tquestions\x18\x01 \x03(\v2;.backend.proto.fulfillment.v1.FulfillmentReportQuestionSyncR\tquestions\"\x96\x01\n" +
	")GetGroomingQuestionsByQuestionKeysRequest\x12i\n" +
	"\rquestion_keys\x18\x01 \x03(\v27.backend.proto.fulfillment.v1.GroomingQuestionUniqueKeyB\v\xfaB\b\x92\x01\x05\b\x01\x10\xd0\x0fR\fquestionKeys\"\x8c\x01\n" +
	"\x19GroomingQuestionUniqueKey\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tcompanyId\x12(\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\n" +
	"businessId\x12\x1d\n" +
	"\x05title\x18\x03 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\x05title\"\x87\x01\n" +
	"*GetGroomingQuestionsByQuestionKeysResponse\x12Y\n" +
	"\tquestions\x18\x01 \x03(\v2;.backend.proto.fulfillment.v1.FulfillmentReportQuestionSyncR\tquestions\"\x87\x01\n" +
	"\x1dGetReportsByUniqueKeysRequest\x12f\n" +
	"\vunique_keys\x18\x01 \x03(\v28.backend.proto.fulfillment.v1.FulfillmentReportUniqueKeyB\v\xfaB\b\x92\x01\x05\b\x01\x10\xd0\x0fR\n" +
	"uniqueKeys\"o\n" +
	"\x1eGetReportsByUniqueKeysResponse\x12M\n" +
	"\areports\x18\x01 \x03(\v23.backend.proto.fulfillment.v1.FulfillmentReportSyncR\areports\"\x80\x01\n" +
	"\x1dGetRecordsByReportKeysRequest\x12_\n" +
	"\vrecord_keys\x18\x01 \x03(\v21.backend.proto.fulfillment.v1.SendRecordUniqueKeyB\v\xfaB\b\x92\x01\x05\b\x01\x10\xd0\x0fR\n" +
	"recordKeys\"\xda\x01\n" +
	"\x13SendRecordUniqueKey\x12n\n" +
	"\x11report_unique_key\x18\x01 \x01(\v28.backend.proto.fulfillment.v1.FulfillmentReportUniqueKeyB\b\xfaB\x05\x8a\x01\x02\x10\x01R\x0freportUniqueKey\x12S\n" +
	"\vsend_method\x18\x02 \x01(\x0e2(.backend.proto.fulfillment.v1.SendMethodB\b\xfaB\x05\x82\x01\x02\x10\x01R\n" +
	"sendMethod\"y\n" +
	"\x1eGetRecordsByReportKeysResponse\x12W\n" +
	"\arecords\x18\x01 \x03(\v2=.backend.proto.fulfillment.v1.FulfillmentReportSendRecordSyncR\arecords*_\n" +
	"\rSyncOperation\x12\x1e\n" +
	"\x1aSYNC_OPERATION_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06CREATE\x10\x01\x12\n" +
	"\n" +
	"\x06UPDATE\x10\x02\x12\n" +
	"\n" +
	"\x06UPSERT\x10\x03\x12\n" +
	"\n" +
	"\x06DELETE\x10\x04*W\n" +
	"\n" +
	"SyncStatus\x12\x1b\n" +
	"\x17SYNC_STATUS_UNSPECIFIED\x10\x00\x12\v\n" +
	"\aSUCCESS\x10\x01\x12\n" +
	"\n" +
	"\x06FAILED\x10\x02\x12\x13\n" +
	"\x0fPARTIAL_SUCCESS\x10\x032\xf2%\n" +
	"\x18FulfillmentReportService\x12\xa5\x01\n" +
	"\x1cGetFulfillmentReportTemplate\x12A.backend.proto.fulfillment.v1.GetFulfillmentReportTemplateRequest\x1aB.backend.proto.fulfillment.v1.GetFulfillmentReportTemplateResponse\x12\xae\x01\n" +
	"\x1fUpdateFulfillmentReportTemplate\x12D.backend.proto.fulfillment.v1.UpdateFulfillmentReportTemplateRequest\x1aE.backend.proto.fulfillment.v1.UpdateFulfillmentReportTemplateResponse\x12\x8d\x01\n" +
	"\x14GetFulfillmentReport\x129.backend.proto.fulfillment.v1.GetFulfillmentReportRequest\x1a:.backend.proto.fulfillment.v1.GetFulfillmentReportResponse\x12\x96\x01\n" +
	"\x17UpdateFulfillmentReport\x12<.backend.proto.fulfillment.v1.UpdateFulfillmentReportRequest\x1a=.backend.proto.fulfillment.v1.UpdateFulfillmentReportResponse\x12\x90\x01\n" +
	"\x15ListFulfillmentReport\x12:.backend.proto.fulfillment.v1.ListFulfillmentReportRequest\x1a;.backend.proto.fulfillment.v1.ListFulfillmentReportResponse\x12\x93\x01\n" +
	"\x16CountFulfillmentReport\x12;.backend.proto.fulfillment.v1.CountFulfillmentReportRequest\x1a<.backend.proto.fulfillment.v1.CountFulfillmentReportResponse\x12\xa5\x01\n" +
	"\x1cBatchDeleteFulfillmentReport\x12A.backend.proto.fulfillment.v1.BatchDeleteFulfillmentReportRequest\x1aB.backend.proto.fulfillment.v1.BatchDeleteFulfillmentReportResponse\x12\x9f\x01\n" +
	"\x1aBatchSendFulfillmentReport\x12?.backend.proto.fulfillment.v1.BatchSendFulfillmentReportRequest\<EMAIL>\x12\xae\x01\n" +
	"\x1fGetFulfillmentReportSummaryInfo\x12D.backend.proto.fulfillment.v1.GetFulfillmentReportSummaryInfoRequest\x1aE.backend.proto.fulfillment.v1.GetFulfillmentReportSummaryInfoResponse\x12\xa2\x01\n" +
	"\x1bGetFulfillmentReportPreview\<EMAIL>\x1aA.backend.proto.fulfillment.v1.GetFulfillmentReportPreviewResponse\x12\x93\x01\n" +
	"\x16GenerateMessageContent\x12;.backend.proto.fulfillment.v1.GenerateMessageContentRequest\x1a<.backend.proto.fulfillment.v1.GenerateMessageContentResponse\x12\x90\x01\n" +
	"\x15SendFulfillmentReport\x12:.backend.proto.fulfillment.v1.SendFulfillmentReportRequest\x1a;.backend.proto.fulfillment.v1.SendFulfillmentReportResponse\x12\xab\x01\n" +
	"\x1eIncreaseFulfillmentOpenedCount\x12C.backend.proto.fulfillment.v1.IncreaseFulfillmentOpenedCountRequest\x1aD.backend.proto.fulfillment.v1.IncreaseFulfillmentOpenedCountResponse\x12\xab\x01\n" +
	"\x1eGetFulfillmentReportSendResult\x12C.backend.proto.fulfillment.v1.GetFulfillmentReportSendResultRequest\x1aD.backend.proto.fulfillment.v1.GetFulfillmentReportSendResultResponse\x12\xae\x01\n" +
	"\x1fGetFulfillmentReportSendHistory\x12D.backend.proto.fulfillment.v1.GetFulfillmentReportSendHistoryRequest\x1aE.backend.proto.fulfillment.v1.GetFulfillmentReportSendHistoryResponse\x12\xa2\x01\n" +
	"\x1bGetFulfillmentReportRecords\<EMAIL>\x1aA.backend.proto.fulfillment.v1.GetFulfillmentReportRecordsResponse\x12\x9f\x01\n" +
	"\x1aListFulfillmentThemeConfig\x12?.backend.proto.fulfillment.v1.ListFulfillmentThemeConfigRequest\<EMAIL>\x12\xa8\x01\n" +
	"\x1dSyncFulfillmentReportTemplate\x12B.backend.proto.fulfillment.v1.SyncFulfillmentReportTemplateRequest\x1aC.backend.proto.fulfillment.v1.SyncFulfillmentReportTemplateResponse\x12\xba\x01\n" +
	"#BatchSyncFulfillmentReportQuestions\x12H.backend.proto.fulfillment.v1.BatchSyncFulfillmentReportQuestionsRequest\x1aI.backend.proto.fulfillment.v1.BatchSyncFulfillmentReportQuestionsResponse\x12\x90\x01\n" +
	"\x15SyncFulfillmentReport\x12:.backend.proto.fulfillment.v1.SyncFulfillmentReportRequest\x1a;.backend.proto.fulfillment.v1.SyncFulfillmentReportResponse\x12\xae\x01\n" +
	"\x1fSyncFulfillmentReportSendRecord\x12D.backend.proto.fulfillment.v1.SyncFulfillmentReportSendRecordRequest\x1aE.backend.proto.fulfillment.v1.SyncFulfillmentReportSendRecordResponse\x12\x90\x01\n" +
	"\x15BatchMigrateTemplates\x12:.backend.proto.fulfillment.v1.BatchMigrateTemplatesRequest\x1a;.backend.proto.fulfillment.v1.BatchMigrateTemplatesResponse\x12\x90\x01\n" +
	"\x15BatchMigrateQuestions\x12:.backend.proto.fulfillment.v1.BatchMigrateQuestionsRequest\x1a;.backend.proto.fulfillment.v1.BatchMigrateQuestionsResponse\x12\x8a\x01\n" +
	"\x13BatchMigrateReports\x128.backend.proto.fulfillment.v1.BatchMigrateReportsRequest\x1a9.backend.proto.fulfillment.v1.BatchMigrateReportsResponse\x12\x8a\x01\n" +
	"\x13BatchMigrateRecords\x128.backend.proto.fulfillment.v1.BatchMigrateRecordsRequest\x1a9.backend.proto.fulfillment.v1.BatchMigrateRecordsResponse\x12\x99\x01\n" +
	"\x18GetTemplatesByUniqueKeys\x12=.backend.proto.fulfillment.v1.GetTemplatesByUniqueKeysRequest\x1a>.backend.proto.fulfillment.v1.GetTemplatesByUniqueKeysResponse\x12\x9f\x01\n" +
	"\x1aGetQuestionsByTemplateKeys\x12?.backend.proto.fulfillment.v1.GetQuestionsByTemplateKeysRequest\<EMAIL>\x12\xb7\x01\n" +
	"\"GetGroomingQuestionsByQuestionKeys\x12G.backend.proto.fulfillment.v1.GetGroomingQuestionsByQuestionKeysRequest\x1aH.backend.proto.fulfillment.v1.GetGroomingQuestionsByQuestionKeysResponse\x12\x93\x01\n" +
	"\x16GetReportsByUniqueKeys\x12;.backend.proto.fulfillment.v1.GetReportsByUniqueKeysRequest\x1a<.backend.proto.fulfillment.v1.GetReportsByUniqueKeysResponse\x12\x93\x01\n" +
	"\x16GetRecordsByReportKeys\x12;.backend.proto.fulfillment.v1.GetRecordsByReportKeysRequest\x1a<.backend.proto.fulfillment.v1.GetRecordsByReportKeysResponseBt\n" +
	"&com.moego.backend.proto.fulfillment.v1P\x01ZHgithub.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpbb\x06proto3"

var (
	file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescOnce sync.Once
	file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescData []byte
)

func file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescGZIP() []byte {
	file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDesc), len(file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDesc)))
	})
	return file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDescData
}

var file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes = make([]protoimpl.MessageInfo, 75)
var file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_goTypes = []any{
	(SyncOperation)(0), // 0: backend.proto.fulfillment.v1.SyncOperation
	(SyncStatus)(0),    // 1: backend.proto.fulfillment.v1.SyncStatus
	(*GetFulfillmentReportTemplateRequest)(nil),                          // 2: backend.proto.fulfillment.v1.GetFulfillmentReportTemplateRequest
	(*GetFulfillmentReportTemplateResponse)(nil),                         // 3: backend.proto.fulfillment.v1.GetFulfillmentReportTemplateResponse
	(*UpdateFulfillmentReportTemplateRequest)(nil),                       // 4: backend.proto.fulfillment.v1.UpdateFulfillmentReportTemplateRequest
	(*UpdateFulfillmentReportTemplateResponse)(nil),                      // 5: backend.proto.fulfillment.v1.UpdateFulfillmentReportTemplateResponse
	(*GetFulfillmentReportRequest)(nil),                                  // 6: backend.proto.fulfillment.v1.GetFulfillmentReportRequest
	(*GetFulfillmentReportResponse)(nil),                                 // 7: backend.proto.fulfillment.v1.GetFulfillmentReportResponse
	(*UpdateFulfillmentReportRequest)(nil),                               // 8: backend.proto.fulfillment.v1.UpdateFulfillmentReportRequest
	(*UpdateFulfillmentReportResponse)(nil),                              // 9: backend.proto.fulfillment.v1.UpdateFulfillmentReportResponse
	(*GetFulfillmentReportSummaryInfoRequest)(nil),                       // 10: backend.proto.fulfillment.v1.GetFulfillmentReportSummaryInfoRequest
	(*GetFulfillmentReportSummaryInfoResponse)(nil),                      // 11: backend.proto.fulfillment.v1.GetFulfillmentReportSummaryInfoResponse
	(*GetFulfillmentReportRecordsRequest)(nil),                           // 12: backend.proto.fulfillment.v1.GetFulfillmentReportRecordsRequest
	(*GetFulfillmentReportRecordsResponse)(nil),                          // 13: backend.proto.fulfillment.v1.GetFulfillmentReportRecordsResponse
	(*GetFulfillmentReportPreviewRequest)(nil),                           // 14: backend.proto.fulfillment.v1.GetFulfillmentReportPreviewRequest
	(*GetFulfillmentReportPreviewResponse)(nil),                          // 15: backend.proto.fulfillment.v1.GetFulfillmentReportPreviewResponse
	(*ListFulfillmentThemeConfigRequest)(nil),                            // 16: backend.proto.fulfillment.v1.ListFulfillmentThemeConfigRequest
	(*ListFulfillmentThemeConfigResponse)(nil),                           // 17: backend.proto.fulfillment.v1.ListFulfillmentThemeConfigResponse
	(*GenerateMessageContentRequest)(nil),                                // 18: backend.proto.fulfillment.v1.GenerateMessageContentRequest
	(*GenerateMessageContentResponse)(nil),                               // 19: backend.proto.fulfillment.v1.GenerateMessageContentResponse
	(*SendFulfillmentReportRequest)(nil),                                 // 20: backend.proto.fulfillment.v1.SendFulfillmentReportRequest
	(*SendFulfillmentReportResponse)(nil),                                // 21: backend.proto.fulfillment.v1.SendFulfillmentReportResponse
	(*GetFulfillmentReportSendHistoryRequest)(nil),                       // 22: backend.proto.fulfillment.v1.GetFulfillmentReportSendHistoryRequest
	(*GetFulfillmentReportSendHistoryResponse)(nil),                      // 23: backend.proto.fulfillment.v1.GetFulfillmentReportSendHistoryResponse
	(*GetFulfillmentReportSendResultRequest)(nil),                        // 24: backend.proto.fulfillment.v1.GetFulfillmentReportSendResultRequest
	(*GetFulfillmentReportSendResultResponse)(nil),                       // 25: backend.proto.fulfillment.v1.GetFulfillmentReportSendResultResponse
	(*ListFulfillmentReportRequest)(nil),                                 // 26: backend.proto.fulfillment.v1.ListFulfillmentReportRequest
	(*ListFulfillmentReportResponse)(nil),                                // 27: backend.proto.fulfillment.v1.ListFulfillmentReportResponse
	(*ListFulfillmentReportConfigFilter)(nil),                            // 28: backend.proto.fulfillment.v1.ListFulfillmentReportConfigFilter
	(*BatchDeleteFulfillmentReportRequest)(nil),                          // 29: backend.proto.fulfillment.v1.BatchDeleteFulfillmentReportRequest
	(*BatchDeleteFulfillmentReportResponse)(nil),                         // 30: backend.proto.fulfillment.v1.BatchDeleteFulfillmentReportResponse
	(*BatchSendFulfillmentReportRequest)(nil),                            // 31: backend.proto.fulfillment.v1.BatchSendFulfillmentReportRequest
	(*BatchSendFulfillmentReportResponse)(nil),                           // 32: backend.proto.fulfillment.v1.BatchSendFulfillmentReportResponse
	(*IncreaseFulfillmentOpenedCountRequest)(nil),                        // 33: backend.proto.fulfillment.v1.IncreaseFulfillmentOpenedCountRequest
	(*IncreaseFulfillmentOpenedCountResponse)(nil),                       // 34: backend.proto.fulfillment.v1.IncreaseFulfillmentOpenedCountResponse
	(*CountFulfillmentReportRequest)(nil),                                // 35: backend.proto.fulfillment.v1.CountFulfillmentReportRequest
	(*CountFulfillmentReportResponse)(nil),                               // 36: backend.proto.fulfillment.v1.CountFulfillmentReportResponse
	(*FulfillmentReportTemplateSync)(nil),                                // 37: backend.proto.fulfillment.v1.FulfillmentReportTemplateSync
	(*FulfillmentReportTemplateUniqueKey)(nil),                           // 38: backend.proto.fulfillment.v1.FulfillmentReportTemplateUniqueKey
	(*SyncFulfillmentReportTemplateRequest)(nil),                         // 39: backend.proto.fulfillment.v1.SyncFulfillmentReportTemplateRequest
	(*SyncFulfillmentReportTemplateResponse)(nil),                        // 40: backend.proto.fulfillment.v1.SyncFulfillmentReportTemplateResponse
	(*FulfillmentReportQuestionSync)(nil),                                // 41: backend.proto.fulfillment.v1.FulfillmentReportQuestionSync
	(*QuestionTemplateIdentifier)(nil),                                   // 42: backend.proto.fulfillment.v1.QuestionTemplateIdentifier
	(*BatchSyncFulfillmentReportQuestionsRequest)(nil),                   // 43: backend.proto.fulfillment.v1.BatchSyncFulfillmentReportQuestionsRequest
	(*BatchSyncFulfillmentReportQuestionsResponse)(nil),                  // 44: backend.proto.fulfillment.v1.BatchSyncFulfillmentReportQuestionsResponse
	(*FulfillmentReportSync)(nil),                                        // 45: backend.proto.fulfillment.v1.FulfillmentReportSync
	(*FulfillmentReportUniqueKey)(nil),                                   // 46: backend.proto.fulfillment.v1.FulfillmentReportUniqueKey
	(*SyncFulfillmentReportRequest)(nil),                                 // 47: backend.proto.fulfillment.v1.SyncFulfillmentReportRequest
	(*SyncFulfillmentReportResponse)(nil),                                // 48: backend.proto.fulfillment.v1.SyncFulfillmentReportResponse
	(*FulfillmentReportSendRecordSync)(nil),                              // 49: backend.proto.fulfillment.v1.FulfillmentReportSendRecordSync
	(*SendRecordIdentifier)(nil),                                         // 50: backend.proto.fulfillment.v1.SendRecordIdentifier
	(*SyncFulfillmentReportSendRecordRequest)(nil),                       // 51: backend.proto.fulfillment.v1.SyncFulfillmentReportSendRecordRequest
	(*SyncFulfillmentReportSendRecordResponse)(nil),                      // 52: backend.proto.fulfillment.v1.SyncFulfillmentReportSendRecordResponse
	(*BatchMigrateTemplatesRequest)(nil),                                 // 53: backend.proto.fulfillment.v1.BatchMigrateTemplatesRequest
	(*BatchMigrateTemplatesResponse)(nil),                                // 54: backend.proto.fulfillment.v1.BatchMigrateTemplatesResponse
	(*BatchMigrateQuestionsRequest)(nil),                                 // 55: backend.proto.fulfillment.v1.BatchMigrateQuestionsRequest
	(*BatchMigrateQuestionsResponse)(nil),                                // 56: backend.proto.fulfillment.v1.BatchMigrateQuestionsResponse
	(*BatchMigrateReportsRequest)(nil),                                   // 57: backend.proto.fulfillment.v1.BatchMigrateReportsRequest
	(*BatchMigrateReportsResponse)(nil),                                  // 58: backend.proto.fulfillment.v1.BatchMigrateReportsResponse
	(*BatchMigrateRecordsRequest)(nil),                                   // 59: backend.proto.fulfillment.v1.BatchMigrateRecordsRequest
	(*BatchMigrateRecordsResponse)(nil),                                  // 60: backend.proto.fulfillment.v1.BatchMigrateRecordsResponse
	(*GetTemplatesByUniqueKeysRequest)(nil),                              // 61: backend.proto.fulfillment.v1.GetTemplatesByUniqueKeysRequest
	(*GetTemplatesByUniqueKeysResponse)(nil),                             // 62: backend.proto.fulfillment.v1.GetTemplatesByUniqueKeysResponse
	(*GetQuestionsByTemplateKeysRequest)(nil),                            // 63: backend.proto.fulfillment.v1.GetQuestionsByTemplateKeysRequest
	(*GetQuestionsByTemplateKeysResponse)(nil),                           // 64: backend.proto.fulfillment.v1.GetQuestionsByTemplateKeysResponse
	(*GetGroomingQuestionsByQuestionKeysRequest)(nil),                    // 65: backend.proto.fulfillment.v1.GetGroomingQuestionsByQuestionKeysRequest
	(*GroomingQuestionUniqueKey)(nil),                                    // 66: backend.proto.fulfillment.v1.GroomingQuestionUniqueKey
	(*GetGroomingQuestionsByQuestionKeysResponse)(nil),                   // 67: backend.proto.fulfillment.v1.GetGroomingQuestionsByQuestionKeysResponse
	(*GetReportsByUniqueKeysRequest)(nil),                                // 68: backend.proto.fulfillment.v1.GetReportsByUniqueKeysRequest
	(*GetReportsByUniqueKeysResponse)(nil),                               // 69: backend.proto.fulfillment.v1.GetReportsByUniqueKeysResponse
	(*GetRecordsByReportKeysRequest)(nil),                                // 70: backend.proto.fulfillment.v1.GetRecordsByReportKeysRequest
	(*SendRecordUniqueKey)(nil),                                          // 71: backend.proto.fulfillment.v1.SendRecordUniqueKey
	(*GetRecordsByReportKeysResponse)(nil),                               // 72: backend.proto.fulfillment.v1.GetRecordsByReportKeysResponse
	(*UpdateFulfillmentReportTemplateRequest_UpdateQuestion)(nil),        // 73: backend.proto.fulfillment.v1.UpdateFulfillmentReportTemplateRequest.UpdateQuestion
	(*GetFulfillmentReportRecordsResponse_FulfillmentReportRecords)(nil), // 74: backend.proto.fulfillment.v1.GetFulfillmentReportRecordsResponse.FulfillmentReportRecords
	(*ListFulfillmentReportResponse_FulfillmentReportCard)(nil),          // 75: backend.proto.fulfillment.v1.ListFulfillmentReportResponse.FulfillmentReportCard
	(*ListFulfillmentReportResponse_FulfillmentReportCard_Pet)(nil),      // 76: backend.proto.fulfillment.v1.ListFulfillmentReportResponse.FulfillmentReportCard.Pet
	(CareType)(0),                                       // 77: backend.proto.fulfillment.v1.CareType
	(*FulfillmentReportTemplate)(nil),                   // 78: backend.proto.fulfillment.v1.FulfillmentReportTemplate
	(NextAppointmentDateFormatType)(0),                  // 79: backend.proto.fulfillment.v1.NextAppointmentDateFormatType
	(*FulfillmentReport)(nil),                           // 80: backend.proto.fulfillment.v1.FulfillmentReport
	(*FulfillmentReportContent)(nil),                    // 81: backend.proto.fulfillment.v1.FulfillmentReportContent
	(*FulfillmentReportCardSummaryInfo)(nil),            // 82: backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo
	(*FulfillmentReportSampleValue)(nil),                // 83: backend.proto.fulfillment.v1.FulfillmentReportSampleValue
	(*FulfillmentReportThemeConfig)(nil),                // 84: backend.proto.fulfillment.v1.FulfillmentReportThemeConfig
	(SendMethod)(0),                                     // 85: backend.proto.fulfillment.v1.SendMethod
	(*FulfillmentReportSendResult)(nil),                 // 86: backend.proto.fulfillment.v1.FulfillmentReportSendResult
	(*FulfillmentReportSendRecord)(nil),                 // 87: backend.proto.fulfillment.v1.FulfillmentReportSendRecord
	(*PaginationRef)(nil),                               // 88: backend.proto.fulfillment.v1.PaginationRef
	(ReportStatus)(0),                                   // 89: backend.proto.fulfillment.v1.ReportStatus
	(*timestamppb.Timestamp)(nil),                       // 90: google.protobuf.Timestamp
	(QuestionCategory)(0),                               // 91: backend.proto.fulfillment.v1.QuestionCategory
	(QuestionType)(0),                                   // 92: backend.proto.fulfillment.v1.QuestionType
	(*FulfillmentReportTemplateQuestion_ExtraInfo)(nil), // 93: backend.proto.fulfillment.v1.FulfillmentReportTemplateQuestion.ExtraInfo
	(v1.Pet_PetType)(0),                                 // 94: backend.proto.pet.v1.Pet.PetType
}
var file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_depIdxs = []int32{
	77,  // 0: backend.proto.fulfillment.v1.GetFulfillmentReportTemplateRequest.care_type:type_name -> backend.proto.fulfillment.v1.CareType
	78,  // 1: backend.proto.fulfillment.v1.GetFulfillmentReportTemplateResponse.template:type_name -> backend.proto.fulfillment.v1.FulfillmentReportTemplate
	77,  // 2: backend.proto.fulfillment.v1.UpdateFulfillmentReportTemplateRequest.care_type:type_name -> backend.proto.fulfillment.v1.CareType
	79,  // 3: backend.proto.fulfillment.v1.UpdateFulfillmentReportTemplateRequest.next_appointment_date_format_type:type_name -> backend.proto.fulfillment.v1.NextAppointmentDateFormatType
	73,  // 4: backend.proto.fulfillment.v1.UpdateFulfillmentReportTemplateRequest.questions:type_name -> backend.proto.fulfillment.v1.UpdateFulfillmentReportTemplateRequest.UpdateQuestion
	77,  // 5: backend.proto.fulfillment.v1.GetFulfillmentReportRequest.care_type:type_name -> backend.proto.fulfillment.v1.CareType
	80,  // 6: backend.proto.fulfillment.v1.GetFulfillmentReportResponse.fulfillment_report:type_name -> backend.proto.fulfillment.v1.FulfillmentReport
	77,  // 7: backend.proto.fulfillment.v1.UpdateFulfillmentReportRequest.care_type:type_name -> backend.proto.fulfillment.v1.CareType
	81,  // 8: backend.proto.fulfillment.v1.UpdateFulfillmentReportRequest.content:type_name -> backend.proto.fulfillment.v1.FulfillmentReportContent
	80,  // 9: backend.proto.fulfillment.v1.UpdateFulfillmentReportResponse.fulfillment_report:type_name -> backend.proto.fulfillment.v1.FulfillmentReport
	82,  // 10: backend.proto.fulfillment.v1.GetFulfillmentReportSummaryInfoResponse.summary_info:type_name -> backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo
	74,  // 11: backend.proto.fulfillment.v1.GetFulfillmentReportRecordsResponse.fulfillment_report_records:type_name -> backend.proto.fulfillment.v1.GetFulfillmentReportRecordsResponse.FulfillmentReportRecords
	77,  // 12: backend.proto.fulfillment.v1.GetFulfillmentReportPreviewRequest.care_type:type_name -> backend.proto.fulfillment.v1.CareType
	82,  // 13: backend.proto.fulfillment.v1.GetFulfillmentReportPreviewResponse.summary_info:type_name -> backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo
	83,  // 14: backend.proto.fulfillment.v1.GetFulfillmentReportPreviewResponse.sample_value:type_name -> backend.proto.fulfillment.v1.FulfillmentReportSampleValue
	84,  // 15: backend.proto.fulfillment.v1.ListFulfillmentThemeConfigResponse.theme_configs:type_name -> backend.proto.fulfillment.v1.FulfillmentReportThemeConfig
	77,  // 16: backend.proto.fulfillment.v1.GenerateMessageContentRequest.care_type:type_name -> backend.proto.fulfillment.v1.CareType
	85,  // 17: backend.proto.fulfillment.v1.SendFulfillmentReportRequest.send_method:type_name -> backend.proto.fulfillment.v1.SendMethod
	86,  // 18: backend.proto.fulfillment.v1.SendFulfillmentReportResponse.send_result:type_name -> backend.proto.fulfillment.v1.FulfillmentReportSendResult
	77,  // 19: backend.proto.fulfillment.v1.GetFulfillmentReportSendHistoryRequest.care_type:type_name -> backend.proto.fulfillment.v1.CareType
	87,  // 20: backend.proto.fulfillment.v1.GetFulfillmentReportSendHistoryResponse.send_records:type_name -> backend.proto.fulfillment.v1.FulfillmentReportSendRecord
	77,  // 21: backend.proto.fulfillment.v1.GetFulfillmentReportSendResultRequest.care_type:type_name -> backend.proto.fulfillment.v1.CareType
	86,  // 22: backend.proto.fulfillment.v1.GetFulfillmentReportSendResultResponse.send_results:type_name -> backend.proto.fulfillment.v1.FulfillmentReportSendResult
	28,  // 23: backend.proto.fulfillment.v1.ListFulfillmentReportRequest.filter:type_name -> backend.proto.fulfillment.v1.ListFulfillmentReportConfigFilter
	88,  // 24: backend.proto.fulfillment.v1.ListFulfillmentReportRequest.pagination:type_name -> backend.proto.fulfillment.v1.PaginationRef
	75,  // 25: backend.proto.fulfillment.v1.ListFulfillmentReportResponse.fulfillment_report_cards:type_name -> backend.proto.fulfillment.v1.ListFulfillmentReportResponse.FulfillmentReportCard
	88,  // 26: backend.proto.fulfillment.v1.ListFulfillmentReportResponse.pagination:type_name -> backend.proto.fulfillment.v1.PaginationRef
	89,  // 27: backend.proto.fulfillment.v1.ListFulfillmentReportConfigFilter.status:type_name -> backend.proto.fulfillment.v1.ReportStatus
	77,  // 28: backend.proto.fulfillment.v1.ListFulfillmentReportConfigFilter.care_types:type_name -> backend.proto.fulfillment.v1.CareType
	85,  // 29: backend.proto.fulfillment.v1.BatchSendFulfillmentReportRequest.send_method:type_name -> backend.proto.fulfillment.v1.SendMethod
	86,  // 30: backend.proto.fulfillment.v1.BatchSendFulfillmentReportResponse.send_results:type_name -> backend.proto.fulfillment.v1.FulfillmentReportSendResult
	28,  // 31: backend.proto.fulfillment.v1.CountFulfillmentReportRequest.filter:type_name -> backend.proto.fulfillment.v1.ListFulfillmentReportConfigFilter
	77,  // 32: backend.proto.fulfillment.v1.FulfillmentReportTemplateSync.care_type:type_name -> backend.proto.fulfillment.v1.CareType
	79,  // 33: backend.proto.fulfillment.v1.FulfillmentReportTemplateSync.next_appointment_date_format_type:type_name -> backend.proto.fulfillment.v1.NextAppointmentDateFormatType
	90,  // 34: backend.proto.fulfillment.v1.FulfillmentReportTemplateSync.last_publish_time:type_name -> google.protobuf.Timestamp
	90,  // 35: backend.proto.fulfillment.v1.FulfillmentReportTemplateSync.create_time:type_name -> google.protobuf.Timestamp
	90,  // 36: backend.proto.fulfillment.v1.FulfillmentReportTemplateSync.update_time:type_name -> google.protobuf.Timestamp
	77,  // 37: backend.proto.fulfillment.v1.FulfillmentReportTemplateUniqueKey.care_type:type_name -> backend.proto.fulfillment.v1.CareType
	0,   // 38: backend.proto.fulfillment.v1.SyncFulfillmentReportTemplateRequest.operation:type_name -> backend.proto.fulfillment.v1.SyncOperation
	38,  // 39: backend.proto.fulfillment.v1.SyncFulfillmentReportTemplateRequest.unique_key:type_name -> backend.proto.fulfillment.v1.FulfillmentReportTemplateUniqueKey
	37,  // 40: backend.proto.fulfillment.v1.SyncFulfillmentReportTemplateRequest.template:type_name -> backend.proto.fulfillment.v1.FulfillmentReportTemplateSync
	1,   // 41: backend.proto.fulfillment.v1.SyncFulfillmentReportTemplateResponse.status:type_name -> backend.proto.fulfillment.v1.SyncStatus
	77,  // 42: backend.proto.fulfillment.v1.FulfillmentReportQuestionSync.care_type:type_name -> backend.proto.fulfillment.v1.CareType
	91,  // 43: backend.proto.fulfillment.v1.FulfillmentReportQuestionSync.category:type_name -> backend.proto.fulfillment.v1.QuestionCategory
	92,  // 44: backend.proto.fulfillment.v1.FulfillmentReportQuestionSync.type:type_name -> backend.proto.fulfillment.v1.QuestionType
	90,  // 45: backend.proto.fulfillment.v1.FulfillmentReportQuestionSync.create_time:type_name -> google.protobuf.Timestamp
	90,  // 46: backend.proto.fulfillment.v1.FulfillmentReportQuestionSync.update_time:type_name -> google.protobuf.Timestamp
	38,  // 47: backend.proto.fulfillment.v1.QuestionTemplateIdentifier.template_unique_key:type_name -> backend.proto.fulfillment.v1.FulfillmentReportTemplateUniqueKey
	0,   // 48: backend.proto.fulfillment.v1.BatchSyncFulfillmentReportQuestionsRequest.operation:type_name -> backend.proto.fulfillment.v1.SyncOperation
	42,  // 49: backend.proto.fulfillment.v1.BatchSyncFulfillmentReportQuestionsRequest.template_identifier:type_name -> backend.proto.fulfillment.v1.QuestionTemplateIdentifier
	41,  // 50: backend.proto.fulfillment.v1.BatchSyncFulfillmentReportQuestionsRequest.questions:type_name -> backend.proto.fulfillment.v1.FulfillmentReportQuestionSync
	1,   // 51: backend.proto.fulfillment.v1.BatchSyncFulfillmentReportQuestionsResponse.status:type_name -> backend.proto.fulfillment.v1.SyncStatus
	77,  // 52: backend.proto.fulfillment.v1.FulfillmentReportSync.care_type:type_name -> backend.proto.fulfillment.v1.CareType
	90,  // 53: backend.proto.fulfillment.v1.FulfillmentReportSync.template_version:type_name -> google.protobuf.Timestamp
	90,  // 54: backend.proto.fulfillment.v1.FulfillmentReportSync.create_time:type_name -> google.protobuf.Timestamp
	90,  // 55: backend.proto.fulfillment.v1.FulfillmentReportSync.update_time:type_name -> google.protobuf.Timestamp
	77,  // 56: backend.proto.fulfillment.v1.FulfillmentReportUniqueKey.care_type:type_name -> backend.proto.fulfillment.v1.CareType
	0,   // 57: backend.proto.fulfillment.v1.SyncFulfillmentReportRequest.operation:type_name -> backend.proto.fulfillment.v1.SyncOperation
	46,  // 58: backend.proto.fulfillment.v1.SyncFulfillmentReportRequest.unique_key:type_name -> backend.proto.fulfillment.v1.FulfillmentReportUniqueKey
	45,  // 59: backend.proto.fulfillment.v1.SyncFulfillmentReportRequest.report:type_name -> backend.proto.fulfillment.v1.FulfillmentReportSync
	1,   // 60: backend.proto.fulfillment.v1.SyncFulfillmentReportResponse.status:type_name -> backend.proto.fulfillment.v1.SyncStatus
	85,  // 61: backend.proto.fulfillment.v1.FulfillmentReportSendRecordSync.send_method:type_name -> backend.proto.fulfillment.v1.SendMethod
	90,  // 62: backend.proto.fulfillment.v1.FulfillmentReportSendRecordSync.sent_time:type_name -> google.protobuf.Timestamp
	90,  // 63: backend.proto.fulfillment.v1.FulfillmentReportSendRecordSync.create_time:type_name -> google.protobuf.Timestamp
	90,  // 64: backend.proto.fulfillment.v1.FulfillmentReportSendRecordSync.update_time:type_name -> google.protobuf.Timestamp
	85,  // 65: backend.proto.fulfillment.v1.SendRecordIdentifier.send_method:type_name -> backend.proto.fulfillment.v1.SendMethod
	46,  // 66: backend.proto.fulfillment.v1.SendRecordIdentifier.report_unique_key:type_name -> backend.proto.fulfillment.v1.FulfillmentReportUniqueKey
	0,   // 67: backend.proto.fulfillment.v1.SyncFulfillmentReportSendRecordRequest.operation:type_name -> backend.proto.fulfillment.v1.SyncOperation
	50,  // 68: backend.proto.fulfillment.v1.SyncFulfillmentReportSendRecordRequest.business_identifier:type_name -> backend.proto.fulfillment.v1.SendRecordIdentifier
	49,  // 69: backend.proto.fulfillment.v1.SyncFulfillmentReportSendRecordRequest.send_record:type_name -> backend.proto.fulfillment.v1.FulfillmentReportSendRecordSync
	1,   // 70: backend.proto.fulfillment.v1.SyncFulfillmentReportSendRecordResponse.status:type_name -> backend.proto.fulfillment.v1.SyncStatus
	37,  // 71: backend.proto.fulfillment.v1.BatchMigrateTemplatesRequest.templates:type_name -> backend.proto.fulfillment.v1.FulfillmentReportTemplateSync
	41,  // 72: backend.proto.fulfillment.v1.BatchMigrateQuestionsRequest.questions:type_name -> backend.proto.fulfillment.v1.FulfillmentReportQuestionSync
	45,  // 73: backend.proto.fulfillment.v1.BatchMigrateReportsRequest.reports:type_name -> backend.proto.fulfillment.v1.FulfillmentReportSync
	49,  // 74: backend.proto.fulfillment.v1.BatchMigrateRecordsRequest.records:type_name -> backend.proto.fulfillment.v1.FulfillmentReportSendRecordSync
	38,  // 75: backend.proto.fulfillment.v1.GetTemplatesByUniqueKeysRequest.unique_keys:type_name -> backend.proto.fulfillment.v1.FulfillmentReportTemplateUniqueKey
	37,  // 76: backend.proto.fulfillment.v1.GetTemplatesByUniqueKeysResponse.templates:type_name -> backend.proto.fulfillment.v1.FulfillmentReportTemplateSync
	38,  // 77: backend.proto.fulfillment.v1.GetQuestionsByTemplateKeysRequest.template_keys:type_name -> backend.proto.fulfillment.v1.FulfillmentReportTemplateUniqueKey
	41,  // 78: backend.proto.fulfillment.v1.GetQuestionsByTemplateKeysResponse.questions:type_name -> backend.proto.fulfillment.v1.FulfillmentReportQuestionSync
	66,  // 79: backend.proto.fulfillment.v1.GetGroomingQuestionsByQuestionKeysRequest.question_keys:type_name -> backend.proto.fulfillment.v1.GroomingQuestionUniqueKey
	41,  // 80: backend.proto.fulfillment.v1.GetGroomingQuestionsByQuestionKeysResponse.questions:type_name -> backend.proto.fulfillment.v1.FulfillmentReportQuestionSync
	46,  // 81: backend.proto.fulfillment.v1.GetReportsByUniqueKeysRequest.unique_keys:type_name -> backend.proto.fulfillment.v1.FulfillmentReportUniqueKey
	45,  // 82: backend.proto.fulfillment.v1.GetReportsByUniqueKeysResponse.reports:type_name -> backend.proto.fulfillment.v1.FulfillmentReportSync
	71,  // 83: backend.proto.fulfillment.v1.GetRecordsByReportKeysRequest.record_keys:type_name -> backend.proto.fulfillment.v1.SendRecordUniqueKey
	46,  // 84: backend.proto.fulfillment.v1.SendRecordUniqueKey.report_unique_key:type_name -> backend.proto.fulfillment.v1.FulfillmentReportUniqueKey
	85,  // 85: backend.proto.fulfillment.v1.SendRecordUniqueKey.send_method:type_name -> backend.proto.fulfillment.v1.SendMethod
	49,  // 86: backend.proto.fulfillment.v1.GetRecordsByReportKeysResponse.records:type_name -> backend.proto.fulfillment.v1.FulfillmentReportSendRecordSync
	91,  // 87: backend.proto.fulfillment.v1.UpdateFulfillmentReportTemplateRequest.UpdateQuestion.category:type_name -> backend.proto.fulfillment.v1.QuestionCategory
	92,  // 88: backend.proto.fulfillment.v1.UpdateFulfillmentReportTemplateRequest.UpdateQuestion.type:type_name -> backend.proto.fulfillment.v1.QuestionType
	93,  // 89: backend.proto.fulfillment.v1.UpdateFulfillmentReportTemplateRequest.UpdateQuestion.extra:type_name -> backend.proto.fulfillment.v1.FulfillmentReportTemplateQuestion.ExtraInfo
	80,  // 90: backend.proto.fulfillment.v1.GetFulfillmentReportRecordsResponse.FulfillmentReportRecords.fulfillment_report:type_name -> backend.proto.fulfillment.v1.FulfillmentReport
	87,  // 91: backend.proto.fulfillment.v1.GetFulfillmentReportRecordsResponse.FulfillmentReportRecords.fulfillment_report_send_records:type_name -> backend.proto.fulfillment.v1.FulfillmentReportSendRecord
	76,  // 92: backend.proto.fulfillment.v1.ListFulfillmentReportResponse.FulfillmentReportCard.pet:type_name -> backend.proto.fulfillment.v1.ListFulfillmentReportResponse.FulfillmentReportCard.Pet
	90,  // 93: backend.proto.fulfillment.v1.ListFulfillmentReportResponse.FulfillmentReportCard.update_time:type_name -> google.protobuf.Timestamp
	90,  // 94: backend.proto.fulfillment.v1.ListFulfillmentReportResponse.FulfillmentReportCard.send_time:type_name -> google.protobuf.Timestamp
	85,  // 95: backend.proto.fulfillment.v1.ListFulfillmentReportResponse.FulfillmentReportCard.send_method:type_name -> backend.proto.fulfillment.v1.SendMethod
	77,  // 96: backend.proto.fulfillment.v1.ListFulfillmentReportResponse.FulfillmentReportCard.care_type:type_name -> backend.proto.fulfillment.v1.CareType
	94,  // 97: backend.proto.fulfillment.v1.ListFulfillmentReportResponse.FulfillmentReportCard.Pet.pet_type:type_name -> backend.proto.pet.v1.Pet.PetType
	2,   // 98: backend.proto.fulfillment.v1.FulfillmentReportService.GetFulfillmentReportTemplate:input_type -> backend.proto.fulfillment.v1.GetFulfillmentReportTemplateRequest
	4,   // 99: backend.proto.fulfillment.v1.FulfillmentReportService.UpdateFulfillmentReportTemplate:input_type -> backend.proto.fulfillment.v1.UpdateFulfillmentReportTemplateRequest
	6,   // 100: backend.proto.fulfillment.v1.FulfillmentReportService.GetFulfillmentReport:input_type -> backend.proto.fulfillment.v1.GetFulfillmentReportRequest
	8,   // 101: backend.proto.fulfillment.v1.FulfillmentReportService.UpdateFulfillmentReport:input_type -> backend.proto.fulfillment.v1.UpdateFulfillmentReportRequest
	26,  // 102: backend.proto.fulfillment.v1.FulfillmentReportService.ListFulfillmentReport:input_type -> backend.proto.fulfillment.v1.ListFulfillmentReportRequest
	35,  // 103: backend.proto.fulfillment.v1.FulfillmentReportService.CountFulfillmentReport:input_type -> backend.proto.fulfillment.v1.CountFulfillmentReportRequest
	29,  // 104: backend.proto.fulfillment.v1.FulfillmentReportService.BatchDeleteFulfillmentReport:input_type -> backend.proto.fulfillment.v1.BatchDeleteFulfillmentReportRequest
	31,  // 105: backend.proto.fulfillment.v1.FulfillmentReportService.BatchSendFulfillmentReport:input_type -> backend.proto.fulfillment.v1.BatchSendFulfillmentReportRequest
	10,  // 106: backend.proto.fulfillment.v1.FulfillmentReportService.GetFulfillmentReportSummaryInfo:input_type -> backend.proto.fulfillment.v1.GetFulfillmentReportSummaryInfoRequest
	14,  // 107: backend.proto.fulfillment.v1.FulfillmentReportService.GetFulfillmentReportPreview:input_type -> backend.proto.fulfillment.v1.GetFulfillmentReportPreviewRequest
	18,  // 108: backend.proto.fulfillment.v1.FulfillmentReportService.GenerateMessageContent:input_type -> backend.proto.fulfillment.v1.GenerateMessageContentRequest
	20,  // 109: backend.proto.fulfillment.v1.FulfillmentReportService.SendFulfillmentReport:input_type -> backend.proto.fulfillment.v1.SendFulfillmentReportRequest
	33,  // 110: backend.proto.fulfillment.v1.FulfillmentReportService.IncreaseFulfillmentOpenedCount:input_type -> backend.proto.fulfillment.v1.IncreaseFulfillmentOpenedCountRequest
	24,  // 111: backend.proto.fulfillment.v1.FulfillmentReportService.GetFulfillmentReportSendResult:input_type -> backend.proto.fulfillment.v1.GetFulfillmentReportSendResultRequest
	22,  // 112: backend.proto.fulfillment.v1.FulfillmentReportService.GetFulfillmentReportSendHistory:input_type -> backend.proto.fulfillment.v1.GetFulfillmentReportSendHistoryRequest
	12,  // 113: backend.proto.fulfillment.v1.FulfillmentReportService.GetFulfillmentReportRecords:input_type -> backend.proto.fulfillment.v1.GetFulfillmentReportRecordsRequest
	16,  // 114: backend.proto.fulfillment.v1.FulfillmentReportService.ListFulfillmentThemeConfig:input_type -> backend.proto.fulfillment.v1.ListFulfillmentThemeConfigRequest
	39,  // 115: backend.proto.fulfillment.v1.FulfillmentReportService.SyncFulfillmentReportTemplate:input_type -> backend.proto.fulfillment.v1.SyncFulfillmentReportTemplateRequest
	43,  // 116: backend.proto.fulfillment.v1.FulfillmentReportService.BatchSyncFulfillmentReportQuestions:input_type -> backend.proto.fulfillment.v1.BatchSyncFulfillmentReportQuestionsRequest
	47,  // 117: backend.proto.fulfillment.v1.FulfillmentReportService.SyncFulfillmentReport:input_type -> backend.proto.fulfillment.v1.SyncFulfillmentReportRequest
	51,  // 118: backend.proto.fulfillment.v1.FulfillmentReportService.SyncFulfillmentReportSendRecord:input_type -> backend.proto.fulfillment.v1.SyncFulfillmentReportSendRecordRequest
	53,  // 119: backend.proto.fulfillment.v1.FulfillmentReportService.BatchMigrateTemplates:input_type -> backend.proto.fulfillment.v1.BatchMigrateTemplatesRequest
	55,  // 120: backend.proto.fulfillment.v1.FulfillmentReportService.BatchMigrateQuestions:input_type -> backend.proto.fulfillment.v1.BatchMigrateQuestionsRequest
	57,  // 121: backend.proto.fulfillment.v1.FulfillmentReportService.BatchMigrateReports:input_type -> backend.proto.fulfillment.v1.BatchMigrateReportsRequest
	59,  // 122: backend.proto.fulfillment.v1.FulfillmentReportService.BatchMigrateRecords:input_type -> backend.proto.fulfillment.v1.BatchMigrateRecordsRequest
	61,  // 123: backend.proto.fulfillment.v1.FulfillmentReportService.GetTemplatesByUniqueKeys:input_type -> backend.proto.fulfillment.v1.GetTemplatesByUniqueKeysRequest
	63,  // 124: backend.proto.fulfillment.v1.FulfillmentReportService.GetQuestionsByTemplateKeys:input_type -> backend.proto.fulfillment.v1.GetQuestionsByTemplateKeysRequest
	65,  // 125: backend.proto.fulfillment.v1.FulfillmentReportService.GetGroomingQuestionsByQuestionKeys:input_type -> backend.proto.fulfillment.v1.GetGroomingQuestionsByQuestionKeysRequest
	68,  // 126: backend.proto.fulfillment.v1.FulfillmentReportService.GetReportsByUniqueKeys:input_type -> backend.proto.fulfillment.v1.GetReportsByUniqueKeysRequest
	70,  // 127: backend.proto.fulfillment.v1.FulfillmentReportService.GetRecordsByReportKeys:input_type -> backend.proto.fulfillment.v1.GetRecordsByReportKeysRequest
	3,   // 128: backend.proto.fulfillment.v1.FulfillmentReportService.GetFulfillmentReportTemplate:output_type -> backend.proto.fulfillment.v1.GetFulfillmentReportTemplateResponse
	5,   // 129: backend.proto.fulfillment.v1.FulfillmentReportService.UpdateFulfillmentReportTemplate:output_type -> backend.proto.fulfillment.v1.UpdateFulfillmentReportTemplateResponse
	7,   // 130: backend.proto.fulfillment.v1.FulfillmentReportService.GetFulfillmentReport:output_type -> backend.proto.fulfillment.v1.GetFulfillmentReportResponse
	9,   // 131: backend.proto.fulfillment.v1.FulfillmentReportService.UpdateFulfillmentReport:output_type -> backend.proto.fulfillment.v1.UpdateFulfillmentReportResponse
	27,  // 132: backend.proto.fulfillment.v1.FulfillmentReportService.ListFulfillmentReport:output_type -> backend.proto.fulfillment.v1.ListFulfillmentReportResponse
	36,  // 133: backend.proto.fulfillment.v1.FulfillmentReportService.CountFulfillmentReport:output_type -> backend.proto.fulfillment.v1.CountFulfillmentReportResponse
	30,  // 134: backend.proto.fulfillment.v1.FulfillmentReportService.BatchDeleteFulfillmentReport:output_type -> backend.proto.fulfillment.v1.BatchDeleteFulfillmentReportResponse
	32,  // 135: backend.proto.fulfillment.v1.FulfillmentReportService.BatchSendFulfillmentReport:output_type -> backend.proto.fulfillment.v1.BatchSendFulfillmentReportResponse
	11,  // 136: backend.proto.fulfillment.v1.FulfillmentReportService.GetFulfillmentReportSummaryInfo:output_type -> backend.proto.fulfillment.v1.GetFulfillmentReportSummaryInfoResponse
	15,  // 137: backend.proto.fulfillment.v1.FulfillmentReportService.GetFulfillmentReportPreview:output_type -> backend.proto.fulfillment.v1.GetFulfillmentReportPreviewResponse
	19,  // 138: backend.proto.fulfillment.v1.FulfillmentReportService.GenerateMessageContent:output_type -> backend.proto.fulfillment.v1.GenerateMessageContentResponse
	21,  // 139: backend.proto.fulfillment.v1.FulfillmentReportService.SendFulfillmentReport:output_type -> backend.proto.fulfillment.v1.SendFulfillmentReportResponse
	34,  // 140: backend.proto.fulfillment.v1.FulfillmentReportService.IncreaseFulfillmentOpenedCount:output_type -> backend.proto.fulfillment.v1.IncreaseFulfillmentOpenedCountResponse
	25,  // 141: backend.proto.fulfillment.v1.FulfillmentReportService.GetFulfillmentReportSendResult:output_type -> backend.proto.fulfillment.v1.GetFulfillmentReportSendResultResponse
	23,  // 142: backend.proto.fulfillment.v1.FulfillmentReportService.GetFulfillmentReportSendHistory:output_type -> backend.proto.fulfillment.v1.GetFulfillmentReportSendHistoryResponse
	13,  // 143: backend.proto.fulfillment.v1.FulfillmentReportService.GetFulfillmentReportRecords:output_type -> backend.proto.fulfillment.v1.GetFulfillmentReportRecordsResponse
	17,  // 144: backend.proto.fulfillment.v1.FulfillmentReportService.ListFulfillmentThemeConfig:output_type -> backend.proto.fulfillment.v1.ListFulfillmentThemeConfigResponse
	40,  // 145: backend.proto.fulfillment.v1.FulfillmentReportService.SyncFulfillmentReportTemplate:output_type -> backend.proto.fulfillment.v1.SyncFulfillmentReportTemplateResponse
	44,  // 146: backend.proto.fulfillment.v1.FulfillmentReportService.BatchSyncFulfillmentReportQuestions:output_type -> backend.proto.fulfillment.v1.BatchSyncFulfillmentReportQuestionsResponse
	48,  // 147: backend.proto.fulfillment.v1.FulfillmentReportService.SyncFulfillmentReport:output_type -> backend.proto.fulfillment.v1.SyncFulfillmentReportResponse
	52,  // 148: backend.proto.fulfillment.v1.FulfillmentReportService.SyncFulfillmentReportSendRecord:output_type -> backend.proto.fulfillment.v1.SyncFulfillmentReportSendRecordResponse
	54,  // 149: backend.proto.fulfillment.v1.FulfillmentReportService.BatchMigrateTemplates:output_type -> backend.proto.fulfillment.v1.BatchMigrateTemplatesResponse
	56,  // 150: backend.proto.fulfillment.v1.FulfillmentReportService.BatchMigrateQuestions:output_type -> backend.proto.fulfillment.v1.BatchMigrateQuestionsResponse
	58,  // 151: backend.proto.fulfillment.v1.FulfillmentReportService.BatchMigrateReports:output_type -> backend.proto.fulfillment.v1.BatchMigrateReportsResponse
	60,  // 152: backend.proto.fulfillment.v1.FulfillmentReportService.BatchMigrateRecords:output_type -> backend.proto.fulfillment.v1.BatchMigrateRecordsResponse
	62,  // 153: backend.proto.fulfillment.v1.FulfillmentReportService.GetTemplatesByUniqueKeys:output_type -> backend.proto.fulfillment.v1.GetTemplatesByUniqueKeysResponse
	64,  // 154: backend.proto.fulfillment.v1.FulfillmentReportService.GetQuestionsByTemplateKeys:output_type -> backend.proto.fulfillment.v1.GetQuestionsByTemplateKeysResponse
	67,  // 155: backend.proto.fulfillment.v1.FulfillmentReportService.GetGroomingQuestionsByQuestionKeys:output_type -> backend.proto.fulfillment.v1.GetGroomingQuestionsByQuestionKeysResponse
	69,  // 156: backend.proto.fulfillment.v1.FulfillmentReportService.GetReportsByUniqueKeys:output_type -> backend.proto.fulfillment.v1.GetReportsByUniqueKeysResponse
	72,  // 157: backend.proto.fulfillment.v1.FulfillmentReportService.GetRecordsByReportKeys:output_type -> backend.proto.fulfillment.v1.GetRecordsByReportKeysResponse
	128, // [128:158] is the sub-list for method output_type
	98,  // [98:128] is the sub-list for method input_type
	98,  // [98:98] is the sub-list for extension type_name
	98,  // [98:98] is the sub-list for extension extendee
	0,   // [0:98] is the sub-list for field type_name
}

func init() { file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_init() }
func file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_init() {
	if File_backend_proto_fulfillment_v1_fulfillment_report_service_proto != nil {
		return
	}
	file_backend_proto_fulfillment_v1_fulfillment_report_proto_init()
	file_backend_proto_fulfillment_v1_common_proto_init()
	file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[0].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[2].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[4].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[6].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[8].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[10].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[12].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[16].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[18].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[22].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[26].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[35].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[37].OneofWrappers = []any{
		(*SyncFulfillmentReportTemplateRequest_TemplateId)(nil),
		(*SyncFulfillmentReportTemplateRequest_UniqueKey)(nil),
	}
	file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[39].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[40].OneofWrappers = []any{
		(*QuestionTemplateIdentifier_TemplateId)(nil),
		(*QuestionTemplateIdentifier_TemplateUniqueKey)(nil),
	}
	file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[43].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[45].OneofWrappers = []any{
		(*SyncFulfillmentReportRequest_ReportId)(nil),
		(*SyncFulfillmentReportRequest_UniqueKey)(nil),
	}
	file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[47].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes[49].OneofWrappers = []any{
		(*SyncFulfillmentReportSendRecordRequest_RecordId)(nil),
		(*SyncFulfillmentReportSendRecordRequest_BusinessIdentifier)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDesc), len(file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   75,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_depIdxs,
		EnumInfos:         file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_enumTypes,
		MessageInfos:      file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_msgTypes,
	}.Build()
	File_backend_proto_fulfillment_v1_fulfillment_report_service_proto = out.File
	file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_goTypes = nil
	file_backend_proto_fulfillment_v1_fulfillment_report_service_proto_depIdxs = nil
}
